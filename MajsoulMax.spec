# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('config', 'config'), ('proto', 'proto'), ('plugin', 'plugin')],
    hiddenimports=['mitmproxy.tools.dump', 'mitmproxy.options', 'mitmproxy.http', 'mitmproxy.ctx', 'liqi_new', 'plugin.helper', 'plugin.mod', 'plugin.update_liqi', 'proto.liqi_pb2', 'proto.config_pb2', 'proto.sheets_pb2', 'proto.basic_pb2', 'ruamel.yaml', 'loguru', 'google.protobuf.json_format'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='MajsoulMax',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
