('D:\\alone\\2.0\\MajsoulMax-main\\build\\MajsoulMax\\PYZ-00.pyz',
 [('OpenSSL',
   'D:\\dev\\Python312\\Lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'D:\\dev\\Python312\\Lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'D:\\dev\\Python312\\Lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'D:\\dev\\Python312\\Lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('__future__', 'D:\\dev\\Python312\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\dev\\Python312\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_argon2_cffi_bindings',
   'D:\\dev\\Python312\\Lib\\site-packages\\_argon2_cffi_bindings\\__init__.py',
   'PYMODULE'),
  ('_compat_pickle', 'D:\\dev\\Python312\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\dev\\Python312\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'D:\\dev\\Python312\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\dev\\Python312\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\dev\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\dev\\Python312\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\dev\\Python312\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_sitebuiltins', 'D:\\dev\\Python312\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'D:\\dev\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\dev\\Python312\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('addons', 'D:\\alone\\2.0\\MajsoulMax-main\\addons.py', 'PYMODULE'),
  ('aioquic',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\__init__.py',
   'PYMODULE'),
  ('aioquic.buffer',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\buffer.py',
   'PYMODULE'),
  ('aioquic.h3',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\h3\\__init__.py',
   'PYMODULE'),
  ('aioquic.h3.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\h3\\connection.py',
   'PYMODULE'),
  ('aioquic.h3.events',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\h3\\events.py',
   'PYMODULE'),
  ('aioquic.h3.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\h3\\exceptions.py',
   'PYMODULE'),
  ('aioquic.quic',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\__init__.py',
   'PYMODULE'),
  ('aioquic.quic.configuration',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\configuration.py',
   'PYMODULE'),
  ('aioquic.quic.congestion',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\congestion\\__init__.py',
   'PYMODULE'),
  ('aioquic.quic.congestion.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\congestion\\base.py',
   'PYMODULE'),
  ('aioquic.quic.congestion.cubic',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\congestion\\cubic.py',
   'PYMODULE'),
  ('aioquic.quic.congestion.reno',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\congestion\\reno.py',
   'PYMODULE'),
  ('aioquic.quic.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\connection.py',
   'PYMODULE'),
  ('aioquic.quic.crypto',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\crypto.py',
   'PYMODULE'),
  ('aioquic.quic.events',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\events.py',
   'PYMODULE'),
  ('aioquic.quic.logger',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\logger.py',
   'PYMODULE'),
  ('aioquic.quic.packet',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\packet.py',
   'PYMODULE'),
  ('aioquic.quic.packet_builder',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\packet_builder.py',
   'PYMODULE'),
  ('aioquic.quic.rangeset',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\rangeset.py',
   'PYMODULE'),
  ('aioquic.quic.recovery',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\recovery.py',
   'PYMODULE'),
  ('aioquic.quic.stream',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\stream.py',
   'PYMODULE'),
  ('aioquic.tls',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\tls.py',
   'PYMODULE'),
  ('argon2',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2\\__init__.py',
   'PYMODULE'),
  ('argon2._legacy',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2\\_legacy.py',
   'PYMODULE'),
  ('argon2._password_hasher',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2\\_password_hasher.py',
   'PYMODULE'),
  ('argon2._typing',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2\\_typing.py',
   'PYMODULE'),
  ('argon2._utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2\\_utils.py',
   'PYMODULE'),
  ('argon2.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2\\exceptions.py',
   'PYMODULE'),
  ('argon2.low_level',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2\\low_level.py',
   'PYMODULE'),
  ('argon2.profiles',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2\\profiles.py',
   'PYMODULE'),
  ('argparse', 'D:\\dev\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('asgiref',
   'D:\\dev\\Python312\\Lib\\site-packages\\asgiref\\__init__.py',
   'PYMODULE'),
  ('asgiref.compatibility',
   'D:\\dev\\Python312\\Lib\\site-packages\\asgiref\\compatibility.py',
   'PYMODULE'),
  ('asgiref.current_thread_executor',
   'D:\\dev\\Python312\\Lib\\site-packages\\asgiref\\current_thread_executor.py',
   'PYMODULE'),
  ('asgiref.local',
   'D:\\dev\\Python312\\Lib\\site-packages\\asgiref\\local.py',
   'PYMODULE'),
  ('asgiref.sync',
   'D:\\dev\\Python312\\Lib\\site-packages\\asgiref\\sync.py',
   'PYMODULE'),
  ('asgiref.wsgi',
   'D:\\dev\\Python312\\Lib\\site-packages\\asgiref\\wsgi.py',
   'PYMODULE'),
  ('ast', 'D:\\dev\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\dev\\Python312\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\dev\\Python312\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\dev\\Python312\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\dev\\Python312\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\dev\\Python312\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\dev\\Python312\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\dev\\Python312\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events', 'D:\\dev\\Python312\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\dev\\Python312\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\dev\\Python312\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\dev\\Python312\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks', 'D:\\dev\\Python312\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'D:\\dev\\Python312\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\dev\\Python312\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\dev\\Python312\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\dev\\Python312\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues', 'D:\\dev\\Python312\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners',
   'D:\\dev\\Python312\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\dev\\Python312\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\dev\\Python312\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\dev\\Python312\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\dev\\Python312\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\dev\\Python312\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\dev\\Python312\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'D:\\dev\\Python312\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads',
   'D:\\dev\\Python312\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\dev\\Python312\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\dev\\Python312\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock', 'D:\\dev\\Python312\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\dev\\Python312\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\dev\\Python312\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\dev\\Python312\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('backports',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\dev\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\dev\\Python312\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\dev\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('blinker',
   'D:\\dev\\Python312\\Lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'D:\\dev\\Python312\\Lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('brotli', 'D:\\dev\\Python312\\Lib\\site-packages\\brotli.py', 'PYMODULE'),
  ('bz2', 'D:\\dev\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\dev\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'D:\\dev\\Python312\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\dev\\Python312\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('chardet',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd', 'D:\\dev\\Python312\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\dev\\Python312\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\dev\\Python312\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'D:\\dev\\Python312\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\dev\\Python312\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\dev\\Python312\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\dev\\Python312\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\dev\\Python312\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\dev\\Python312\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\dev\\Python312\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\dev\\Python312\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\dev\\Python312\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\dev\\Python312\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\dev\\Python312\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'D:\\dev\\Python312\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'D:\\dev\\Python312\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\dev\\Python312\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\dev\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('crypt', 'D:\\dev\\Python312\\Lib\\crypt.py', 'PYMODULE'),
  ('cryptography',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hmac',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.hkdf',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\hkdf.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'D:\\dev\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\dev\\Python312\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\dev\\Python312\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\dev\\Python312\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses', 'D:\\dev\\Python312\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'D:\\dev\\Python312\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('dataclasses', 'D:\\dev\\Python312\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\dev\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('dbm', 'D:\\dev\\Python312\\Lib\\dbm\\__init__.py', 'PYMODULE'),
  ('dbm.dumb', 'D:\\dev\\Python312\\Lib\\dbm\\dumb.py', 'PYMODULE'),
  ('dbm.gnu', 'D:\\dev\\Python312\\Lib\\dbm\\gnu.py', 'PYMODULE'),
  ('dbm.ndbm', 'D:\\dev\\Python312\\Lib\\dbm\\ndbm.py', 'PYMODULE'),
  ('decimal', 'D:\\dev\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\dev\\Python312\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\dev\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\dev\\Python312\\Lib\\doctest.py', 'PYMODULE'),
  ('dotenv',
   'D:\\dev\\Python312\\Lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'D:\\dev\\Python312\\Lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'D:\\dev\\Python312\\Lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'D:\\dev\\Python312\\Lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email', 'D:\\dev\\Python312\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\dev\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\dev\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\dev\\Python312\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\dev\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\dev\\Python312\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset', 'D:\\dev\\Python312\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\dev\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\dev\\Python312\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\dev\\Python312\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser',
   'D:\\dev\\Python312\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\dev\\Python312\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header', 'D:\\dev\\Python312\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\dev\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\dev\\Python312\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message', 'D:\\dev\\Python312\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\dev\\Python312\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\dev\\Python312\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime',
   'D:\\dev\\Python312\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'D:\\dev\\Python312\\Lib\\email\\utils.py', 'PYMODULE'),
  ('flask',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.app',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.sansio', '-', 'PYMODULE'),
  ('flask.sansio.app',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\sansio\\app.py',
   'PYMODULE'),
  ('flask.sansio.blueprints',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\sansio\\blueprints.py',
   'PYMODULE'),
  ('flask.sansio.scaffold',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\sansio\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.wrappers',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\dev\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\dev\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\dev\\Python312\\Lib\\ftplib.py', 'PYMODULE'),
  ('future',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\__init__.py',
   'PYMODULE'),
  ('future.backports',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\__init__.py',
   'PYMODULE'),
  ('future.backports.datetime',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\datetime.py',
   'PYMODULE'),
  ('future.backports.email',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\__init__.py',
   'PYMODULE'),
  ('future.backports.email._encoded_words',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\_encoded_words.py',
   'PYMODULE'),
  ('future.backports.email._parseaddr',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\_parseaddr.py',
   'PYMODULE'),
  ('future.backports.email._policybase',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\_policybase.py',
   'PYMODULE'),
  ('future.backports.email.base64mime',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\base64mime.py',
   'PYMODULE'),
  ('future.backports.email.charset',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\charset.py',
   'PYMODULE'),
  ('future.backports.email.encoders',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\encoders.py',
   'PYMODULE'),
  ('future.backports.email.errors',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\errors.py',
   'PYMODULE'),
  ('future.backports.email.feedparser',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\feedparser.py',
   'PYMODULE'),
  ('future.backports.email.generator',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\generator.py',
   'PYMODULE'),
  ('future.backports.email.header',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\header.py',
   'PYMODULE'),
  ('future.backports.email.iterators',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\iterators.py',
   'PYMODULE'),
  ('future.backports.email.message',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\message.py',
   'PYMODULE'),
  ('future.backports.email.parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\parser.py',
   'PYMODULE'),
  ('future.backports.email.quoprimime',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\quoprimime.py',
   'PYMODULE'),
  ('future.backports.email.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\utils.py',
   'PYMODULE'),
  ('future.backports.http',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\http\\__init__.py',
   'PYMODULE'),
  ('future.backports.http.client',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\http\\client.py',
   'PYMODULE'),
  ('future.backports.http.cookiejar',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\http\\cookiejar.py',
   'PYMODULE'),
  ('future.backports.misc',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\misc.py',
   'PYMODULE'),
  ('future.backports.urllib',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\urllib\\__init__.py',
   'PYMODULE'),
  ('future.backports.urllib.error',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\urllib\\error.py',
   'PYMODULE'),
  ('future.backports.urllib.parse',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\urllib\\parse.py',
   'PYMODULE'),
  ('future.backports.urllib.request',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\urllib\\request.py',
   'PYMODULE'),
  ('future.backports.urllib.response',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\urllib\\response.py',
   'PYMODULE'),
  ('future.backports.urllib.robotparser',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\urllib\\robotparser.py',
   'PYMODULE'),
  ('future.builtins',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\builtins\\__init__.py',
   'PYMODULE'),
  ('future.builtins.iterators',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\builtins\\iterators.py',
   'PYMODULE'),
  ('future.builtins.misc',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\builtins\\misc.py',
   'PYMODULE'),
  ('future.builtins.new_min_max',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\builtins\\new_min_max.py',
   'PYMODULE'),
  ('future.builtins.newnext',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\builtins\\newnext.py',
   'PYMODULE'),
  ('future.builtins.newround',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\builtins\\newround.py',
   'PYMODULE'),
  ('future.builtins.newsuper',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\builtins\\newsuper.py',
   'PYMODULE'),
  ('future.moves',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\moves\\__init__.py',
   'PYMODULE'),
  ('future.moves.dbm',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\moves\\dbm\\__init__.py',
   'PYMODULE'),
  ('future.moves.dbm.dumb',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\moves\\dbm\\dumb.py',
   'PYMODULE'),
  ('future.moves.dbm.gnu',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\moves\\dbm\\gnu.py',
   'PYMODULE'),
  ('future.moves.dbm.ndbm',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\moves\\dbm\\ndbm.py',
   'PYMODULE'),
  ('future.moves.test',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\moves\\test\\__init__.py',
   'PYMODULE'),
  ('future.moves.test.support',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\moves\\test\\support.py',
   'PYMODULE'),
  ('future.standard_library',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\standard_library\\__init__.py',
   'PYMODULE'),
  ('future.types',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\types\\__init__.py',
   'PYMODULE'),
  ('future.types.newbytes',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\types\\newbytes.py',
   'PYMODULE'),
  ('future.types.newdict',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\types\\newdict.py',
   'PYMODULE'),
  ('future.types.newint',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\types\\newint.py',
   'PYMODULE'),
  ('future.types.newlist',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\types\\newlist.py',
   'PYMODULE'),
  ('future.types.newobject',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\types\\newobject.py',
   'PYMODULE'),
  ('future.types.newrange',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\types\\newrange.py',
   'PYMODULE'),
  ('future.types.newstr',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\types\\newstr.py',
   'PYMODULE'),
  ('future.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\utils\\__init__.py',
   'PYMODULE'),
  ('future.utils.surrogateescape',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\utils\\surrogateescape.py',
   'PYMODULE'),
  ('getopt', 'D:\\dev\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\dev\\Python312\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\dev\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\dev\\Python312\\Lib\\glob.py', 'PYMODULE'),
  ('google', '-', 'PYMODULE'),
  ('google.protobuf',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\__init__.py',
   'PYMODULE'),
  ('google.protobuf.descriptor',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\descriptor.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_database',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\descriptor_database.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_pb2',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\descriptor_pb2.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_pool',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\descriptor_pool.py',
   'PYMODULE'),
  ('google.protobuf.internal',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\__init__.py',
   'PYMODULE'),
  ('google.protobuf.internal.api_implementation',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\api_implementation.py',
   'PYMODULE'),
  ('google.protobuf.internal.builder',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\builder.py',
   'PYMODULE'),
  ('google.protobuf.internal.containers',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\containers.py',
   'PYMODULE'),
  ('google.protobuf.internal.decoder',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\decoder.py',
   'PYMODULE'),
  ('google.protobuf.internal.encoder',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\encoder.py',
   'PYMODULE'),
  ('google.protobuf.internal.enum_type_wrapper',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\enum_type_wrapper.py',
   'PYMODULE'),
  ('google.protobuf.internal.extension_dict',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\extension_dict.py',
   'PYMODULE'),
  ('google.protobuf.internal.message_listener',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\message_listener.py',
   'PYMODULE'),
  ('google.protobuf.internal.python_message',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\python_message.py',
   'PYMODULE'),
  ('google.protobuf.internal.type_checkers',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\type_checkers.py',
   'PYMODULE'),
  ('google.protobuf.internal.well_known_types',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\well_known_types.py',
   'PYMODULE'),
  ('google.protobuf.internal.wire_format',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\wire_format.py',
   'PYMODULE'),
  ('google.protobuf.json_format',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\json_format.py',
   'PYMODULE'),
  ('google.protobuf.message',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\message.py',
   'PYMODULE'),
  ('google.protobuf.message_factory',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\message_factory.py',
   'PYMODULE'),
  ('google.protobuf.pyext',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\pyext\\__init__.py',
   'PYMODULE'),
  ('google.protobuf.pyext.cpp_message',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\pyext\\cpp_message.py',
   'PYMODULE'),
  ('google.protobuf.reflection',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\reflection.py',
   'PYMODULE'),
  ('google.protobuf.service',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\service.py',
   'PYMODULE'),
  ('google.protobuf.service_reflection',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\service_reflection.py',
   'PYMODULE'),
  ('google.protobuf.symbol_database',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\symbol_database.py',
   'PYMODULE'),
  ('google.protobuf.text_encoding',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\text_encoding.py',
   'PYMODULE'),
  ('google.protobuf.text_format',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\text_format.py',
   'PYMODULE'),
  ('gzip', 'D:\\dev\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('h11',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._abnf',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._events',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._headers',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._readers',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._state',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._util',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._version',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._writers',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('h2', 'D:\\dev\\Python312\\Lib\\site-packages\\h2\\__init__.py', 'PYMODULE'),
  ('h2.config',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\config.py',
   'PYMODULE'),
  ('h2.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\connection.py',
   'PYMODULE'),
  ('h2.errors',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\errors.py',
   'PYMODULE'),
  ('h2.events',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\events.py',
   'PYMODULE'),
  ('h2.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\exceptions.py',
   'PYMODULE'),
  ('h2.frame_buffer',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\frame_buffer.py',
   'PYMODULE'),
  ('h2.settings',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\settings.py',
   'PYMODULE'),
  ('h2.stream',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\stream.py',
   'PYMODULE'),
  ('h2.utilities',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\utilities.py',
   'PYMODULE'),
  ('h2.windows',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\windows.py',
   'PYMODULE'),
  ('hashlib', 'D:\\dev\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\dev\\Python312\\Lib\\hmac.py', 'PYMODULE'),
  ('hpack',
   'D:\\dev\\Python312\\Lib\\site-packages\\hpack\\__init__.py',
   'PYMODULE'),
  ('hpack.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\hpack\\exceptions.py',
   'PYMODULE'),
  ('hpack.hpack',
   'D:\\dev\\Python312\\Lib\\site-packages\\hpack\\hpack.py',
   'PYMODULE'),
  ('hpack.huffman',
   'D:\\dev\\Python312\\Lib\\site-packages\\hpack\\huffman.py',
   'PYMODULE'),
  ('hpack.huffman_constants',
   'D:\\dev\\Python312\\Lib\\site-packages\\hpack\\huffman_constants.py',
   'PYMODULE'),
  ('hpack.huffman_table',
   'D:\\dev\\Python312\\Lib\\site-packages\\hpack\\huffman_table.py',
   'PYMODULE'),
  ('hpack.struct',
   'D:\\dev\\Python312\\Lib\\site-packages\\hpack\\struct.py',
   'PYMODULE'),
  ('hpack.table',
   'D:\\dev\\Python312\\Lib\\site-packages\\hpack\\table.py',
   'PYMODULE'),
  ('html', 'D:\\dev\\Python312\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\dev\\Python312\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'D:\\dev\\Python312\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\dev\\Python312\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\dev\\Python312\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'D:\\dev\\Python312\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'D:\\dev\\Python312\\Lib\\http\\server.py', 'PYMODULE'),
  ('hyperframe',
   'D:\\dev\\Python312\\Lib\\site-packages\\hyperframe\\__init__.py',
   'PYMODULE'),
  ('hyperframe.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\hyperframe\\exceptions.py',
   'PYMODULE'),
  ('hyperframe.flags',
   'D:\\dev\\Python312\\Lib\\site-packages\\hyperframe\\flags.py',
   'PYMODULE'),
  ('hyperframe.frame',
   'D:\\dev\\Python312\\Lib\\site-packages\\hyperframe\\frame.py',
   'PYMODULE'),
  ('idna',
   'D:\\dev\\Python312\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\dev\\Python312\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\dev\\Python312\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\dev\\Python312\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\dev\\Python312\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\dev\\Python312\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'D:\\dev\\Python312\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\dev\\Python312\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\dev\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\dev\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\dev\\Python312\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\dev\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\dev\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\dev\\Python312\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\dev\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\dev\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('itsdangerous',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jinja2',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'D:\\dev\\Python312\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\dev\\Python312\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\dev\\Python312\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\dev\\Python312\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('kaitaistruct',
   'D:\\dev\\Python312\\Lib\\site-packages\\kaitaistruct.py',
   'PYMODULE'),
  ('ldap3',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\__init__.py',
   'PYMODULE'),
  ('ldap3.abstract',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\abstract\\__init__.py',
   'PYMODULE'),
  ('ldap3.abstract.attrDef',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\abstract\\attrDef.py',
   'PYMODULE'),
  ('ldap3.abstract.attribute',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\abstract\\attribute.py',
   'PYMODULE'),
  ('ldap3.abstract.cursor',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\abstract\\cursor.py',
   'PYMODULE'),
  ('ldap3.abstract.entry',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\abstract\\entry.py',
   'PYMODULE'),
  ('ldap3.abstract.objectDef',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\abstract\\objectDef.py',
   'PYMODULE'),
  ('ldap3.core',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\__init__.py',
   'PYMODULE'),
  ('ldap3.core.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\connection.py',
   'PYMODULE'),
  ('ldap3.core.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\exceptions.py',
   'PYMODULE'),
  ('ldap3.core.pooling',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\pooling.py',
   'PYMODULE'),
  ('ldap3.core.rdns',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\rdns.py',
   'PYMODULE'),
  ('ldap3.core.results',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\results.py',
   'PYMODULE'),
  ('ldap3.core.server',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\server.py',
   'PYMODULE'),
  ('ldap3.core.timezone',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\timezone.py',
   'PYMODULE'),
  ('ldap3.core.tls',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\tls.py',
   'PYMODULE'),
  ('ldap3.core.usage',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\usage.py',
   'PYMODULE'),
  ('ldap3.extend',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\__init__.py',
   'PYMODULE'),
  ('ldap3.extend.microsoft',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\microsoft\\__init__.py',
   'PYMODULE'),
  ('ldap3.extend.microsoft.addMembersToGroups',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\microsoft\\addMembersToGroups.py',
   'PYMODULE'),
  ('ldap3.extend.microsoft.dirSync',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\microsoft\\dirSync.py',
   'PYMODULE'),
  ('ldap3.extend.microsoft.modifyPassword',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\microsoft\\modifyPassword.py',
   'PYMODULE'),
  ('ldap3.extend.microsoft.persistentSearch',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\microsoft\\persistentSearch.py',
   'PYMODULE'),
  ('ldap3.extend.microsoft.removeMembersFromGroups',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\microsoft\\removeMembersFromGroups.py',
   'PYMODULE'),
  ('ldap3.extend.microsoft.unlockAccount',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\microsoft\\unlockAccount.py',
   'PYMODULE'),
  ('ldap3.extend.novell',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\__init__.py',
   'PYMODULE'),
  ('ldap3.extend.novell.addMembersToGroups',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\addMembersToGroups.py',
   'PYMODULE'),
  ('ldap3.extend.novell.checkGroupsMemberships',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\checkGroupsMemberships.py',
   'PYMODULE'),
  ('ldap3.extend.novell.endTransaction',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\endTransaction.py',
   'PYMODULE'),
  ('ldap3.extend.novell.getBindDn',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\getBindDn.py',
   'PYMODULE'),
  ('ldap3.extend.novell.listReplicas',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\listReplicas.py',
   'PYMODULE'),
  ('ldap3.extend.novell.nmasGetUniversalPassword',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\nmasGetUniversalPassword.py',
   'PYMODULE'),
  ('ldap3.extend.novell.nmasSetUniversalPassword',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\nmasSetUniversalPassword.py',
   'PYMODULE'),
  ('ldap3.extend.novell.partition_entry_count',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\partition_entry_count.py',
   'PYMODULE'),
  ('ldap3.extend.novell.removeMembersFromGroups',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\removeMembersFromGroups.py',
   'PYMODULE'),
  ('ldap3.extend.novell.replicaInfo',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\replicaInfo.py',
   'PYMODULE'),
  ('ldap3.extend.novell.startTransaction',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\startTransaction.py',
   'PYMODULE'),
  ('ldap3.extend.operation',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\operation.py',
   'PYMODULE'),
  ('ldap3.extend.standard',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\standard\\__init__.py',
   'PYMODULE'),
  ('ldap3.extend.standard.PagedSearch',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\standard\\PagedSearch.py',
   'PYMODULE'),
  ('ldap3.extend.standard.PersistentSearch',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\standard\\PersistentSearch.py',
   'PYMODULE'),
  ('ldap3.extend.standard.modifyPassword',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\standard\\modifyPassword.py',
   'PYMODULE'),
  ('ldap3.extend.standard.whoAmI',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\standard\\whoAmI.py',
   'PYMODULE'),
  ('ldap3.operation',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\__init__.py',
   'PYMODULE'),
  ('ldap3.operation.abandon',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\abandon.py',
   'PYMODULE'),
  ('ldap3.operation.add',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\add.py',
   'PYMODULE'),
  ('ldap3.operation.bind',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\bind.py',
   'PYMODULE'),
  ('ldap3.operation.compare',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\compare.py',
   'PYMODULE'),
  ('ldap3.operation.delete',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\delete.py',
   'PYMODULE'),
  ('ldap3.operation.extended',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\extended.py',
   'PYMODULE'),
  ('ldap3.operation.modify',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\modify.py',
   'PYMODULE'),
  ('ldap3.operation.modifyDn',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\modifyDn.py',
   'PYMODULE'),
  ('ldap3.operation.search',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\search.py',
   'PYMODULE'),
  ('ldap3.operation.unbind',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\unbind.py',
   'PYMODULE'),
  ('ldap3.protocol',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\__init__.py',
   'PYMODULE'),
  ('ldap3.protocol.controls',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\controls.py',
   'PYMODULE'),
  ('ldap3.protocol.convert',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\convert.py',
   'PYMODULE'),
  ('ldap3.protocol.formatters',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\formatters\\__init__.py',
   'PYMODULE'),
  ('ldap3.protocol.formatters.formatters',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\formatters\\formatters.py',
   'PYMODULE'),
  ('ldap3.protocol.formatters.standard',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\formatters\\standard.py',
   'PYMODULE'),
  ('ldap3.protocol.formatters.validators',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\formatters\\validators.py',
   'PYMODULE'),
  ('ldap3.protocol.microsoft',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\microsoft.py',
   'PYMODULE'),
  ('ldap3.protocol.novell',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\novell.py',
   'PYMODULE'),
  ('ldap3.protocol.oid',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\oid.py',
   'PYMODULE'),
  ('ldap3.protocol.persistentSearch',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\persistentSearch.py',
   'PYMODULE'),
  ('ldap3.protocol.rfc2696',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\rfc2696.py',
   'PYMODULE'),
  ('ldap3.protocol.rfc2849',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\rfc2849.py',
   'PYMODULE'),
  ('ldap3.protocol.rfc3062',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\rfc3062.py',
   'PYMODULE'),
  ('ldap3.protocol.rfc4511',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\rfc4511.py',
   'PYMODULE'),
  ('ldap3.protocol.rfc4512',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\rfc4512.py',
   'PYMODULE'),
  ('ldap3.protocol.sasl',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\sasl\\__init__.py',
   'PYMODULE'),
  ('ldap3.protocol.sasl.digestMd5',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\sasl\\digestMd5.py',
   'PYMODULE'),
  ('ldap3.protocol.sasl.external',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\sasl\\external.py',
   'PYMODULE'),
  ('ldap3.protocol.sasl.kerberos',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\sasl\\kerberos.py',
   'PYMODULE'),
  ('ldap3.protocol.sasl.plain',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\sasl\\plain.py',
   'PYMODULE'),
  ('ldap3.protocol.sasl.sasl',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\sasl\\sasl.py',
   'PYMODULE'),
  ('ldap3.protocol.schemas',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\schemas\\__init__.py',
   'PYMODULE'),
  ('ldap3.protocol.schemas.ad2012R2',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\schemas\\ad2012R2.py',
   'PYMODULE'),
  ('ldap3.protocol.schemas.ds389',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\schemas\\ds389.py',
   'PYMODULE'),
  ('ldap3.protocol.schemas.edir888',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\schemas\\edir888.py',
   'PYMODULE'),
  ('ldap3.protocol.schemas.edir914',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\schemas\\edir914.py',
   'PYMODULE'),
  ('ldap3.protocol.schemas.slapd24',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\schemas\\slapd24.py',
   'PYMODULE'),
  ('ldap3.strategy',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\__init__.py',
   'PYMODULE'),
  ('ldap3.strategy.asyncStream',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\asyncStream.py',
   'PYMODULE'),
  ('ldap3.strategy.asynchronous',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\asynchronous.py',
   'PYMODULE'),
  ('ldap3.strategy.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\base.py',
   'PYMODULE'),
  ('ldap3.strategy.ldifProducer',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\ldifProducer.py',
   'PYMODULE'),
  ('ldap3.strategy.mockAsync',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\mockAsync.py',
   'PYMODULE'),
  ('ldap3.strategy.mockBase',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\mockBase.py',
   'PYMODULE'),
  ('ldap3.strategy.mockSync',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\mockSync.py',
   'PYMODULE'),
  ('ldap3.strategy.restartable',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\restartable.py',
   'PYMODULE'),
  ('ldap3.strategy.reusable',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\reusable.py',
   'PYMODULE'),
  ('ldap3.strategy.safeRestartable',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\safeRestartable.py',
   'PYMODULE'),
  ('ldap3.strategy.safeSync',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\safeSync.py',
   'PYMODULE'),
  ('ldap3.strategy.sync',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\sync.py',
   'PYMODULE'),
  ('ldap3.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\__init__.py',
   'PYMODULE'),
  ('ldap3.utils.asn1',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\asn1.py',
   'PYMODULE'),
  ('ldap3.utils.ciDict',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\ciDict.py',
   'PYMODULE'),
  ('ldap3.utils.config',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\config.py',
   'PYMODULE'),
  ('ldap3.utils.conv',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\conv.py',
   'PYMODULE'),
  ('ldap3.utils.dn',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\dn.py',
   'PYMODULE'),
  ('ldap3.utils.hashed',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\hashed.py',
   'PYMODULE'),
  ('ldap3.utils.log',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\log.py',
   'PYMODULE'),
  ('ldap3.utils.ntlm',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\ntlm.py',
   'PYMODULE'),
  ('ldap3.utils.ordDict',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\ordDict.py',
   'PYMODULE'),
  ('ldap3.utils.port_validators',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\port_validators.py',
   'PYMODULE'),
  ('ldap3.utils.repr',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\repr.py',
   'PYMODULE'),
  ('ldap3.utils.tls_backport',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\tls_backport.py',
   'PYMODULE'),
  ('ldap3.utils.uri',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\uri.py',
   'PYMODULE'),
  ('ldap3.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\version.py',
   'PYMODULE'),
  ('liqi_new', 'D:\\alone\\2.0\\MajsoulMax-main\\liqi_new.py', 'PYMODULE'),
  ('logging', 'D:\\dev\\Python312\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('logging.handlers',
   'D:\\dev\\Python312\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('loguru',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\__init__.py',
   'PYMODULE'),
  ('loguru._asyncio_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_asyncio_loop.py',
   'PYMODULE'),
  ('loguru._better_exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_better_exceptions.py',
   'PYMODULE'),
  ('loguru._colorama',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_colorama.py',
   'PYMODULE'),
  ('loguru._colorizer',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_colorizer.py',
   'PYMODULE'),
  ('loguru._contextvars',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_contextvars.py',
   'PYMODULE'),
  ('loguru._ctime_functions',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_ctime_functions.py',
   'PYMODULE'),
  ('loguru._datetime',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_datetime.py',
   'PYMODULE'),
  ('loguru._defaults',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_defaults.py',
   'PYMODULE'),
  ('loguru._error_interceptor',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_error_interceptor.py',
   'PYMODULE'),
  ('loguru._file_sink',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_file_sink.py',
   'PYMODULE'),
  ('loguru._filters',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_filters.py',
   'PYMODULE'),
  ('loguru._get_frame',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_get_frame.py',
   'PYMODULE'),
  ('loguru._handler',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_handler.py',
   'PYMODULE'),
  ('loguru._locks_machinery',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_locks_machinery.py',
   'PYMODULE'),
  ('loguru._logger',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_logger.py',
   'PYMODULE'),
  ('loguru._recattrs',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_recattrs.py',
   'PYMODULE'),
  ('loguru._simple_sinks',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_simple_sinks.py',
   'PYMODULE'),
  ('loguru._string_parsers',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_string_parsers.py',
   'PYMODULE'),
  ('lzma', 'D:\\dev\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'D:\\dev\\Python312\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\dev\\Python312\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'D:\\dev\\Python312\\Lib\\mimetypes.py', 'PYMODULE'),
  ('mitmproxy',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.addonmanager',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addonmanager.py',
   'PYMODULE'),
  ('mitmproxy.addons',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.addons.anticache',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\anticache.py',
   'PYMODULE'),
  ('mitmproxy.addons.anticomp',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\anticomp.py',
   'PYMODULE'),
  ('mitmproxy.addons.asgiapp',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\asgiapp.py',
   'PYMODULE'),
  ('mitmproxy.addons.block',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\block.py',
   'PYMODULE'),
  ('mitmproxy.addons.blocklist',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\blocklist.py',
   'PYMODULE'),
  ('mitmproxy.addons.browser',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\browser.py',
   'PYMODULE'),
  ('mitmproxy.addons.clientplayback',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\clientplayback.py',
   'PYMODULE'),
  ('mitmproxy.addons.command_history',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\command_history.py',
   'PYMODULE'),
  ('mitmproxy.addons.comment',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\comment.py',
   'PYMODULE'),
  ('mitmproxy.addons.core',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\core.py',
   'PYMODULE'),
  ('mitmproxy.addons.cut',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\cut.py',
   'PYMODULE'),
  ('mitmproxy.addons.disable_h2c',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\disable_h2c.py',
   'PYMODULE'),
  ('mitmproxy.addons.dns_resolver',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\dns_resolver.py',
   'PYMODULE'),
  ('mitmproxy.addons.dumper',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\dumper.py',
   'PYMODULE'),
  ('mitmproxy.addons.errorcheck',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\errorcheck.py',
   'PYMODULE'),
  ('mitmproxy.addons.eventstore',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\eventstore.py',
   'PYMODULE'),
  ('mitmproxy.addons.export',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\export.py',
   'PYMODULE'),
  ('mitmproxy.addons.intercept',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\intercept.py',
   'PYMODULE'),
  ('mitmproxy.addons.keepserving',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\keepserving.py',
   'PYMODULE'),
  ('mitmproxy.addons.maplocal',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\maplocal.py',
   'PYMODULE'),
  ('mitmproxy.addons.mapremote',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\mapremote.py',
   'PYMODULE'),
  ('mitmproxy.addons.modifybody',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\modifybody.py',
   'PYMODULE'),
  ('mitmproxy.addons.modifyheaders',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\modifyheaders.py',
   'PYMODULE'),
  ('mitmproxy.addons.next_layer',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\next_layer.py',
   'PYMODULE'),
  ('mitmproxy.addons.onboarding',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\onboarding.py',
   'PYMODULE'),
  ('mitmproxy.addons.onboardingapp',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\onboardingapp\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.addons.proxyauth',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\proxyauth.py',
   'PYMODULE'),
  ('mitmproxy.addons.proxyserver',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\proxyserver.py',
   'PYMODULE'),
  ('mitmproxy.addons.readfile',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\readfile.py',
   'PYMODULE'),
  ('mitmproxy.addons.save',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\save.py',
   'PYMODULE'),
  ('mitmproxy.addons.savehar',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\savehar.py',
   'PYMODULE'),
  ('mitmproxy.addons.script',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\script.py',
   'PYMODULE'),
  ('mitmproxy.addons.serverplayback',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\serverplayback.py',
   'PYMODULE'),
  ('mitmproxy.addons.stickyauth',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\stickyauth.py',
   'PYMODULE'),
  ('mitmproxy.addons.stickycookie',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\stickycookie.py',
   'PYMODULE'),
  ('mitmproxy.addons.strip_dns_https_records',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\strip_dns_https_records.py',
   'PYMODULE'),
  ('mitmproxy.addons.termlog',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\termlog.py',
   'PYMODULE'),
  ('mitmproxy.addons.tlsconfig',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\tlsconfig.py',
   'PYMODULE'),
  ('mitmproxy.addons.update_alt_svc',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\update_alt_svc.py',
   'PYMODULE'),
  ('mitmproxy.addons.upstream_auth',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\upstream_auth.py',
   'PYMODULE'),
  ('mitmproxy.addons.view',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\view.py',
   'PYMODULE'),
  ('mitmproxy.certs',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\certs.py',
   'PYMODULE'),
  ('mitmproxy.command',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\command.py',
   'PYMODULE'),
  ('mitmproxy.command_lexer',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\command_lexer.py',
   'PYMODULE'),
  ('mitmproxy.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\connection.py',
   'PYMODULE'),
  ('mitmproxy.contentviews',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._api',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_api.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_compat.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._registry',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_registry.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_utils.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_css',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_css.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_dns',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_dns.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_graphql',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_graphql.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_http3',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_http3.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_image',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_image\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_image.image_parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_image\\image_parser.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_image.view',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_image\\view.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_javascript',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_javascript.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_json',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_json.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_mqtt',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_mqtt.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_multipart',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_multipart.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_query',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_query.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_raw',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_raw.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_socketio',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_socketio.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_urlencoded',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_urlencoded.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_wbxml',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_wbxml.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_xml_html',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_xml_html.py',
   'PYMODULE'),
  ('mitmproxy.contentviews.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\base.py',
   'PYMODULE'),
  ('mitmproxy.contrib',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.contrib.click',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\click\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.contrib.imghdr',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\imghdr.py',
   'PYMODULE'),
  ('mitmproxy.contrib.kaitaistruct',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\kaitaistruct\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.contrib.kaitaistruct.dtls_client_hello',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\kaitaistruct\\dtls_client_hello.py',
   'PYMODULE'),
  ('mitmproxy.contrib.kaitaistruct.exif',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\kaitaistruct\\exif.py',
   'PYMODULE'),
  ('mitmproxy.contrib.kaitaistruct.gif',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\kaitaistruct\\gif.py',
   'PYMODULE'),
  ('mitmproxy.contrib.kaitaistruct.ico',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\kaitaistruct\\ico.py',
   'PYMODULE'),
  ('mitmproxy.contrib.kaitaistruct.jpeg',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\kaitaistruct\\jpeg.py',
   'PYMODULE'),
  ('mitmproxy.contrib.kaitaistruct.png',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\kaitaistruct\\png.py',
   'PYMODULE'),
  ('mitmproxy.contrib.kaitaistruct.tls_client_hello',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\kaitaistruct\\tls_client_hello.py',
   'PYMODULE'),
  ('mitmproxy.contrib.wbxml',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\wbxml\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.contrib.wbxml.ASCommandResponse',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\wbxml\\ASCommandResponse.py',
   'PYMODULE'),
  ('mitmproxy.contrib.wbxml.ASWBXML',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\wbxml\\ASWBXML.py',
   'PYMODULE'),
  ('mitmproxy.contrib.wbxml.ASWBXMLByteQueue',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\wbxml\\ASWBXMLByteQueue.py',
   'PYMODULE'),
  ('mitmproxy.contrib.wbxml.ASWBXMLCodePage',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\wbxml\\ASWBXMLCodePage.py',
   'PYMODULE'),
  ('mitmproxy.contrib.wbxml.GlobalTokens',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\wbxml\\GlobalTokens.py',
   'PYMODULE'),
  ('mitmproxy.contrib.wbxml.InvalidDataException',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\wbxml\\InvalidDataException.py',
   'PYMODULE'),
  ('mitmproxy.coretypes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\coretypes\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.coretypes.multidict',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\coretypes\\multidict.py',
   'PYMODULE'),
  ('mitmproxy.coretypes.serializable',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\coretypes\\serializable.py',
   'PYMODULE'),
  ('mitmproxy.ctx',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\ctx.py',
   'PYMODULE'),
  ('mitmproxy.dns',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\dns.py',
   'PYMODULE'),
  ('mitmproxy.eventsequence',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\eventsequence.py',
   'PYMODULE'),
  ('mitmproxy.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\exceptions.py',
   'PYMODULE'),
  ('mitmproxy.flow',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\flow.py',
   'PYMODULE'),
  ('mitmproxy.flowfilter',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\flowfilter.py',
   'PYMODULE'),
  ('mitmproxy.hooks',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\hooks.py',
   'PYMODULE'),
  ('mitmproxy.http',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\http.py',
   'PYMODULE'),
  ('mitmproxy.io',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\io\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.io.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\io\\compat.py',
   'PYMODULE'),
  ('mitmproxy.io.har',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\io\\har.py',
   'PYMODULE'),
  ('mitmproxy.io.io',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\io\\io.py',
   'PYMODULE'),
  ('mitmproxy.io.tnetstring',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\io\\tnetstring.py',
   'PYMODULE'),
  ('mitmproxy.log',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\log.py',
   'PYMODULE'),
  ('mitmproxy.master',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\master.py',
   'PYMODULE'),
  ('mitmproxy.net',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.net.check',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\check.py',
   'PYMODULE'),
  ('mitmproxy.net.dns',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\dns\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.net.dns.classes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\dns\\classes.py',
   'PYMODULE'),
  ('mitmproxy.net.dns.domain_names',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\dns\\domain_names.py',
   'PYMODULE'),
  ('mitmproxy.net.dns.https_records',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\dns\\https_records.py',
   'PYMODULE'),
  ('mitmproxy.net.dns.op_codes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\dns\\op_codes.py',
   'PYMODULE'),
  ('mitmproxy.net.dns.response_codes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\dns\\response_codes.py',
   'PYMODULE'),
  ('mitmproxy.net.dns.types',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\dns\\types.py',
   'PYMODULE'),
  ('mitmproxy.net.encoding',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\encoding.py',
   'PYMODULE'),
  ('mitmproxy.net.free_port',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\free_port.py',
   'PYMODULE'),
  ('mitmproxy.net.http',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.net.http.cookies',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\cookies.py',
   'PYMODULE'),
  ('mitmproxy.net.http.headers',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\headers.py',
   'PYMODULE'),
  ('mitmproxy.net.http.http1',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\http1\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.net.http.http1.assemble',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\http1\\assemble.py',
   'PYMODULE'),
  ('mitmproxy.net.http.http1.read',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\http1\\read.py',
   'PYMODULE'),
  ('mitmproxy.net.http.multipart',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\multipart.py',
   'PYMODULE'),
  ('mitmproxy.net.http.status_codes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\status_codes.py',
   'PYMODULE'),
  ('mitmproxy.net.http.url',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\url.py',
   'PYMODULE'),
  ('mitmproxy.net.http.validate',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\validate.py',
   'PYMODULE'),
  ('mitmproxy.net.local_ip',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\local_ip.py',
   'PYMODULE'),
  ('mitmproxy.net.server_spec',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\server_spec.py',
   'PYMODULE'),
  ('mitmproxy.net.tls',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\tls.py',
   'PYMODULE'),
  ('mitmproxy.options',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\options.py',
   'PYMODULE'),
  ('mitmproxy.optmanager',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\optmanager.py',
   'PYMODULE'),
  ('mitmproxy.platform',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\platform\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.platform.linux',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\platform\\linux.py',
   'PYMODULE'),
  ('mitmproxy.platform.openbsd',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\platform\\openbsd.py',
   'PYMODULE'),
  ('mitmproxy.platform.osx',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\platform\\osx.py',
   'PYMODULE'),
  ('mitmproxy.platform.pf',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\platform\\pf.py',
   'PYMODULE'),
  ('mitmproxy.platform.windows',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\platform\\windows.py',
   'PYMODULE'),
  ('mitmproxy.proxy',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.proxy.commands',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\commands.py',
   'PYMODULE'),
  ('mitmproxy.proxy.context',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\context.py',
   'PYMODULE'),
  ('mitmproxy.proxy.events',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\events.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layer',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layer.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.dns',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\dns.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._base',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_base.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._events',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_events.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._hooks',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_hooks.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._http1',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_http1.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._http2',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_http2.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._http3',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_http3.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._http_h2',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_http_h2.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._http_h3',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_http_h3.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._upstream_proxy',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_upstream_proxy.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.modes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\modes.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.quic',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\quic\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.quic._client_hello_parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\quic\\_client_hello_parser.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.quic._commands',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\quic\\_commands.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.quic._events',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\quic\\_events.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.quic._hooks',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\quic\\_hooks.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.quic._raw_layers',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\quic\\_raw_layers.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.quic._stream_layers',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\quic\\_stream_layers.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.tcp',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\tcp.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.tls',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\tls.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.udp',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\udp.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.websocket',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\websocket.py',
   'PYMODULE'),
  ('mitmproxy.proxy.mode_servers',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\mode_servers.py',
   'PYMODULE'),
  ('mitmproxy.proxy.mode_specs',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\mode_specs.py',
   'PYMODULE'),
  ('mitmproxy.proxy.server',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\server.py',
   'PYMODULE'),
  ('mitmproxy.proxy.server_hooks',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\server_hooks.py',
   'PYMODULE'),
  ('mitmproxy.proxy.tunnel',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\tunnel.py',
   'PYMODULE'),
  ('mitmproxy.proxy.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\utils.py',
   'PYMODULE'),
  ('mitmproxy.script',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\script\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.script.concurrent',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\script\\concurrent.py',
   'PYMODULE'),
  ('mitmproxy.tcp',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tcp.py',
   'PYMODULE'),
  ('mitmproxy.tls',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tls.py',
   'PYMODULE'),
  ('mitmproxy.tools',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.tools.console',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.commander',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\commander\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.commander.commander',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\commander\\commander.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.commandexecutor',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\commandexecutor.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.commands',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\commands.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.common',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\common.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.consoleaddons',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\consoleaddons.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.defaultkeys',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\defaultkeys.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.eventlog',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\eventlog.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.flowdetailview',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\flowdetailview.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.flowlist',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\flowlist.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.flowview',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\flowview.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.grideditor',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\grideditor\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.grideditor.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\grideditor\\base.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.grideditor.col_bytes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\grideditor\\col_bytes.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.grideditor.col_subgrid',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\grideditor\\col_subgrid.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.grideditor.col_text',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\grideditor\\col_text.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.grideditor.col_viewany',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\grideditor\\col_viewany.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.grideditor.editors',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\grideditor\\editors.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.help',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\help.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.keybindings',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\keybindings.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.keymap',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\keymap.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.layoutwidget',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\layoutwidget.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.master',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\master.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.options',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\options.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.overlay',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\overlay.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.palettes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\palettes.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.quickhelp',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\quickhelp.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.searchable',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\searchable.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.signals',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\signals.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.statusbar',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\statusbar.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.tabs',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\tabs.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.window',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\window.py',
   'PYMODULE'),
  ('mitmproxy.tools.dump',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\dump.py',
   'PYMODULE'),
  ('mitmproxy.types',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\types.py',
   'PYMODULE'),
  ('mitmproxy.udp',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\udp.py',
   'PYMODULE'),
  ('mitmproxy.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.utils.asyncio_utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\asyncio_utils.py',
   'PYMODULE'),
  ('mitmproxy.utils.data',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\data.py',
   'PYMODULE'),
  ('mitmproxy.utils.emoji',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\emoji.py',
   'PYMODULE'),
  ('mitmproxy.utils.human',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\human.py',
   'PYMODULE'),
  ('mitmproxy.utils.magisk',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\magisk.py',
   'PYMODULE'),
  ('mitmproxy.utils.signals',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\signals.py',
   'PYMODULE'),
  ('mitmproxy.utils.sliding_window',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\sliding_window.py',
   'PYMODULE'),
  ('mitmproxy.utils.spec',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\spec.py',
   'PYMODULE'),
  ('mitmproxy.utils.strutils',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\strutils.py',
   'PYMODULE'),
  ('mitmproxy.utils.typecheck',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\typecheck.py',
   'PYMODULE'),
  ('mitmproxy.utils.vt_codes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\vt_codes.py',
   'PYMODULE'),
  ('mitmproxy.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\version.py',
   'PYMODULE'),
  ('mitmproxy.websocket',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\websocket.py',
   'PYMODULE'),
  ('mitmproxy_rs',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_rs\\__init__.py',
   'PYMODULE'),
  ('mitmproxy_windows',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_windows\\__init__.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\dev\\Python312\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\dev\\Python312\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\dev\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\dev\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\dev\\Python312\\Lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('passlib',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\__init__.py',
   'PYMODULE'),
  ('passlib.apache',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\apache.py',
   'PYMODULE'),
  ('passlib.context',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\context.py',
   'PYMODULE'),
  ('passlib.crypto',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\__init__.py',
   'PYMODULE'),
  ('passlib.crypto._blowfish',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\_blowfish\\__init__.py',
   'PYMODULE'),
  ('passlib.crypto._blowfish.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\_blowfish\\base.py',
   'PYMODULE'),
  ('passlib.crypto._blowfish.unrolled',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\_blowfish\\unrolled.py',
   'PYMODULE'),
  ('passlib.crypto._md4',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\_md4.py',
   'PYMODULE'),
  ('passlib.crypto.des',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\des.py',
   'PYMODULE'),
  ('passlib.crypto.digest',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\digest.py',
   'PYMODULE'),
  ('passlib.crypto.scrypt',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\scrypt\\__init__.py',
   'PYMODULE'),
  ('passlib.crypto.scrypt._builtin',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\scrypt\\_builtin.py',
   'PYMODULE'),
  ('passlib.crypto.scrypt._salsa',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\scrypt\\_salsa.py',
   'PYMODULE'),
  ('passlib.exc',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\exc.py',
   'PYMODULE'),
  ('passlib.handlers',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\__init__.py',
   'PYMODULE'),
  ('passlib.handlers.argon2',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\argon2.py',
   'PYMODULE'),
  ('passlib.handlers.bcrypt',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\bcrypt.py',
   'PYMODULE'),
  ('passlib.handlers.cisco',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\cisco.py',
   'PYMODULE'),
  ('passlib.handlers.des_crypt',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\des_crypt.py',
   'PYMODULE'),
  ('passlib.handlers.digests',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\digests.py',
   'PYMODULE'),
  ('passlib.handlers.django',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\django.py',
   'PYMODULE'),
  ('passlib.handlers.fshp',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\fshp.py',
   'PYMODULE'),
  ('passlib.handlers.ldap_digests',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\ldap_digests.py',
   'PYMODULE'),
  ('passlib.handlers.md5_crypt',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\md5_crypt.py',
   'PYMODULE'),
  ('passlib.handlers.misc',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\misc.py',
   'PYMODULE'),
  ('passlib.handlers.mssql',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\mssql.py',
   'PYMODULE'),
  ('passlib.handlers.mysql',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\mysql.py',
   'PYMODULE'),
  ('passlib.handlers.oracle',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\oracle.py',
   'PYMODULE'),
  ('passlib.handlers.pbkdf2',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\pbkdf2.py',
   'PYMODULE'),
  ('passlib.handlers.phpass',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\phpass.py',
   'PYMODULE'),
  ('passlib.handlers.postgres',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\postgres.py',
   'PYMODULE'),
  ('passlib.handlers.roundup',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\roundup.py',
   'PYMODULE'),
  ('passlib.handlers.scram',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\scram.py',
   'PYMODULE'),
  ('passlib.handlers.scrypt',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\scrypt.py',
   'PYMODULE'),
  ('passlib.handlers.sha1_crypt',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\sha1_crypt.py',
   'PYMODULE'),
  ('passlib.handlers.sha2_crypt',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\sha2_crypt.py',
   'PYMODULE'),
  ('passlib.handlers.sun_md5_crypt',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\sun_md5_crypt.py',
   'PYMODULE'),
  ('passlib.handlers.windows',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\windows.py',
   'PYMODULE'),
  ('passlib.hash',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\hash.py',
   'PYMODULE'),
  ('passlib.ifc',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\ifc.py',
   'PYMODULE'),
  ('passlib.registry',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\registry.py',
   'PYMODULE'),
  ('passlib.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\utils\\__init__.py',
   'PYMODULE'),
  ('passlib.utils.binary',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\utils\\binary.py',
   'PYMODULE'),
  ('passlib.utils.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\utils\\compat\\__init__.py',
   'PYMODULE'),
  ('passlib.utils.decor',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\utils\\decor.py',
   'PYMODULE'),
  ('passlib.utils.handlers',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\utils\\handlers.py',
   'PYMODULE'),
  ('pathlib', 'D:\\dev\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\dev\\Python312\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\dev\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\dev\\Python312\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\dev\\Python312\\Lib\\platform.py', 'PYMODULE'),
  ('plugin', '-', 'PYMODULE'),
  ('plugin.helper',
   'D:\\alone\\2.0\\MajsoulMax-main\\plugin\\helper.py',
   'PYMODULE'),
  ('plugin.mod', 'D:\\alone\\2.0\\MajsoulMax-main\\plugin\\mod.py', 'PYMODULE'),
  ('plugin.update_liqi',
   'D:\\alone\\2.0\\MajsoulMax-main\\plugin\\update_liqi.py',
   'PYMODULE'),
  ('pprint', 'D:\\dev\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('proto', '-', 'PYMODULE'),
  ('proto.basic_pb2',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\basic_pb2.py',
   'PYMODULE'),
  ('proto.config_pb2',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\config_pb2.py',
   'PYMODULE'),
  ('proto.liqi_pb2',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\liqi_pb2.py',
   'PYMODULE'),
  ('proto.sheets_pb2',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\sheets_pb2.py',
   'PYMODULE'),
  ('pty', 'D:\\dev\\Python312\\Lib\\pty.py', 'PYMODULE'),
  ('publicsuffix2',
   'D:\\dev\\Python312\\Lib\\site-packages\\publicsuffix2\\__init__.py',
   'PYMODULE'),
  ('py_compile', 'D:\\dev\\Python312\\Lib\\py_compile.py', 'PYMODULE'),
  ('pyasn1',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\ber\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.decoder',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\ber\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.encoder',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\ber\\encoder.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.eoo',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\ber\\eoo.py',
   'PYMODULE'),
  ('pyasn1.codec.cer',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\cer\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.cer.decoder',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\cer\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.der',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\der\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.der.decoder',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\der\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.streaming',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\streaming.py',
   'PYMODULE'),
  ('pyasn1.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\compat\\__init__.py',
   'PYMODULE'),
  ('pyasn1.compat.integer',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\compat\\integer.py',
   'PYMODULE'),
  ('pyasn1.debug',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\debug.py',
   'PYMODULE'),
  ('pyasn1.error',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\error.py',
   'PYMODULE'),
  ('pyasn1.type',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\__init__.py',
   'PYMODULE'),
  ('pyasn1.type.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\base.py',
   'PYMODULE'),
  ('pyasn1.type.char',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\char.py',
   'PYMODULE'),
  ('pyasn1.type.constraint',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\constraint.py',
   'PYMODULE'),
  ('pyasn1.type.error',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\error.py',
   'PYMODULE'),
  ('pyasn1.type.namedtype',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\namedtype.py',
   'PYMODULE'),
  ('pyasn1.type.namedval',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\namedval.py',
   'PYMODULE'),
  ('pyasn1.type.tag',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\tag.py',
   'PYMODULE'),
  ('pyasn1.type.tagmap',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\tagmap.py',
   'PYMODULE'),
  ('pyasn1.type.univ',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\univ.py',
   'PYMODULE'),
  ('pyasn1.type.useful',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\useful.py',
   'PYMODULE'),
  ('pydivert',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\__init__.py',
   'PYMODULE'),
  ('pydivert.consts',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\consts.py',
   'PYMODULE'),
  ('pydivert.packet',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\packet\\__init__.py',
   'PYMODULE'),
  ('pydivert.packet.header',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\packet\\header.py',
   'PYMODULE'),
  ('pydivert.packet.icmp',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\packet\\icmp.py',
   'PYMODULE'),
  ('pydivert.packet.ip',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\packet\\ip.py',
   'PYMODULE'),
  ('pydivert.packet.tcp',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\packet\\tcp.py',
   'PYMODULE'),
  ('pydivert.packet.udp',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\packet\\udp.py',
   'PYMODULE'),
  ('pydivert.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\util.py',
   'PYMODULE'),
  ('pydivert.windivert',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\windivert.py',
   'PYMODULE'),
  ('pydivert.windivert_dll',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\windivert_dll\\__init__.py',
   'PYMODULE'),
  ('pydivert.windivert_dll.structs',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\windivert_dll\\structs.py',
   'PYMODULE'),
  ('pydoc', 'D:\\dev\\Python312\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\dev\\Python312\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\dev\\Python312\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pylsqpack',
   'D:\\dev\\Python312\\Lib\\site-packages\\pylsqpack\\__init__.py',
   'PYMODULE'),
  ('pyparsing',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pyperclip',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('queue', 'D:\\dev\\Python312\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\dev\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\dev\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter', 'D:\\dev\\Python312\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('ruamel', '-', 'PYMODULE'),
  ('ruamel.yaml',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\__init__.py',
   'PYMODULE'),
  ('ruamel.yaml.anchor',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\anchor.py',
   'PYMODULE'),
  ('ruamel.yaml.comments',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\comments.py',
   'PYMODULE'),
  ('ruamel.yaml.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\compat.py',
   'PYMODULE'),
  ('ruamel.yaml.composer',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\composer.py',
   'PYMODULE'),
  ('ruamel.yaml.constructor',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\constructor.py',
   'PYMODULE'),
  ('ruamel.yaml.cyaml',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\cyaml.py',
   'PYMODULE'),
  ('ruamel.yaml.docinfo',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\docinfo.py',
   'PYMODULE'),
  ('ruamel.yaml.dumper',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\dumper.py',
   'PYMODULE'),
  ('ruamel.yaml.emitter',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\emitter.py',
   'PYMODULE'),
  ('ruamel.yaml.error',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\error.py',
   'PYMODULE'),
  ('ruamel.yaml.events',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\events.py',
   'PYMODULE'),
  ('ruamel.yaml.loader',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\loader.py',
   'PYMODULE'),
  ('ruamel.yaml.main',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\main.py',
   'PYMODULE'),
  ('ruamel.yaml.nodes',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\nodes.py',
   'PYMODULE'),
  ('ruamel.yaml.parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\parser.py',
   'PYMODULE'),
  ('ruamel.yaml.reader',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\reader.py',
   'PYMODULE'),
  ('ruamel.yaml.representer',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\representer.py',
   'PYMODULE'),
  ('ruamel.yaml.resolver',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\resolver.py',
   'PYMODULE'),
  ('ruamel.yaml.scalarbool',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\scalarbool.py',
   'PYMODULE'),
  ('ruamel.yaml.scalarfloat',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\scalarfloat.py',
   'PYMODULE'),
  ('ruamel.yaml.scalarint',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\scalarint.py',
   'PYMODULE'),
  ('ruamel.yaml.scalarstring',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\scalarstring.py',
   'PYMODULE'),
  ('ruamel.yaml.scanner',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\scanner.py',
   'PYMODULE'),
  ('ruamel.yaml.serializer',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\serializer.py',
   'PYMODULE'),
  ('ruamel.yaml.tag',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\tag.py',
   'PYMODULE'),
  ('ruamel.yaml.timestamp',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\timestamp.py',
   'PYMODULE'),
  ('ruamel.yaml.tokens',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\tokens.py',
   'PYMODULE'),
  ('ruamel.yaml.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\util.py',
   'PYMODULE'),
  ('runpy', 'D:\\dev\\Python312\\Lib\\runpy.py', 'PYMODULE'),
  ('sched', 'D:\\dev\\Python312\\Lib\\sched.py', 'PYMODULE'),
  ('secrets', 'D:\\dev\\Python312\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\dev\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('service_identity',
   'D:\\dev\\Python312\\Lib\\site-packages\\service_identity\\__init__.py',
   'PYMODULE'),
  ('service_identity.cryptography',
   'D:\\dev\\Python312\\Lib\\site-packages\\service_identity\\cryptography.py',
   'PYMODULE'),
  ('service_identity.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\service_identity\\exceptions.py',
   'PYMODULE'),
  ('service_identity.hazmat',
   'D:\\dev\\Python312\\Lib\\site-packages\\service_identity\\hazmat.py',
   'PYMODULE'),
  ('service_identity.pyopenssl',
   'D:\\dev\\Python312\\Lib\\site-packages\\service_identity\\pyopenssl.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\dev\\Python312\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\dev\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\dev\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\dev\\Python312\\Lib\\site.py', 'PYMODULE'),
  ('smtplib', 'D:\\dev\\Python312\\Lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'D:\\dev\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\dev\\Python312\\Lib\\socketserver.py', 'PYMODULE'),
  ('sortedcontainers',
   'D:\\dev\\Python312\\Lib\\site-packages\\sortedcontainers\\__init__.py',
   'PYMODULE'),
  ('sortedcontainers.sorteddict',
   'D:\\dev\\Python312\\Lib\\site-packages\\sortedcontainers\\sorteddict.py',
   'PYMODULE'),
  ('sortedcontainers.sortedlist',
   'D:\\dev\\Python312\\Lib\\site-packages\\sortedcontainers\\sortedlist.py',
   'PYMODULE'),
  ('sortedcontainers.sortedset',
   'D:\\dev\\Python312\\Lib\\site-packages\\sortedcontainers\\sortedset.py',
   'PYMODULE'),
  ('ssl', 'D:\\dev\\Python312\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\dev\\Python312\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\dev\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\dev\\Python312\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\dev\\Python312\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\dev\\Python312\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\dev\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\dev\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('test', 'D:\\dev\\Python312\\Lib\\test\\__init__.py', 'PYMODULE'),
  ('test.support',
   'D:\\dev\\Python312\\Lib\\test\\support\\__init__.py',
   'PYMODULE'),
  ('test.support.import_helper',
   'D:\\dev\\Python312\\Lib\\test\\support\\import_helper.py',
   'PYMODULE'),
  ('test.support.os_helper',
   'D:\\dev\\Python312\\Lib\\test\\support\\os_helper.py',
   'PYMODULE'),
  ('test.support.script_helper',
   'D:\\dev\\Python312\\Lib\\test\\support\\script_helper.py',
   'PYMODULE'),
  ('test.support.socket_helper',
   'D:\\dev\\Python312\\Lib\\test\\support\\socket_helper.py',
   'PYMODULE'),
  ('test.support.warnings_helper',
   'D:\\dev\\Python312\\Lib\\test\\support\\warnings_helper.py',
   'PYMODULE'),
  ('test.test_support',
   'D:\\dev\\Python312\\Lib\\test\\test_support.py',
   'PYMODULE'),
  ('textwrap', 'D:\\dev\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\dev\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('timeit', 'D:\\dev\\Python312\\Lib\\timeit.py', 'PYMODULE'),
  ('tkinter', 'D:\\dev\\Python312\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.constants',
   'D:\\dev\\Python312\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('token', 'D:\\dev\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\dev\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'D:\\dev\\Python312\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser',
   'D:\\dev\\Python312\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re', 'D:\\dev\\Python312\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'D:\\dev\\Python312\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tornado',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\__init__.py',
   'PYMODULE'),
  ('tornado.concurrent',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\concurrent.py',
   'PYMODULE'),
  ('tornado.escape',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\escape.py',
   'PYMODULE'),
  ('tornado.gen',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\gen.py',
   'PYMODULE'),
  ('tornado.ioloop',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\ioloop.py',
   'PYMODULE'),
  ('tornado.iostream',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\iostream.py',
   'PYMODULE'),
  ('tornado.log',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\log.py',
   'PYMODULE'),
  ('tornado.netutil',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\netutil.py',
   'PYMODULE'),
  ('tornado.options',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\options.py',
   'PYMODULE'),
  ('tornado.platform',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\platform\\__init__.py',
   'PYMODULE'),
  ('tornado.platform.asyncio',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\platform\\asyncio.py',
   'PYMODULE'),
  ('tornado.process',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\process.py',
   'PYMODULE'),
  ('tornado.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\util.py',
   'PYMODULE'),
  ('tracemalloc', 'D:\\dev\\Python312\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'D:\\dev\\Python312\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\dev\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\dev\\Python312\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest', 'D:\\dev\\Python312\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'D:\\dev\\Python312\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\dev\\Python312\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'D:\\dev\\Python312\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader',
   'D:\\dev\\Python312\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main', 'D:\\dev\\Python312\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'D:\\dev\\Python312\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result',
   'D:\\dev\\Python312\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\dev\\Python312\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\dev\\Python312\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite', 'D:\\dev\\Python312\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'D:\\dev\\Python312\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'D:\\dev\\Python312\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\dev\\Python312\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\dev\\Python312\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'D:\\dev\\Python312\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\dev\\Python312\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urwid',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\__init__.py',
   'PYMODULE'),
  ('urwid.canvas',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\canvas.py',
   'PYMODULE'),
  ('urwid.command_map',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\command_map.py',
   'PYMODULE'),
  ('urwid.display',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\__init__.py',
   'PYMODULE'),
  ('urwid.display._posix_raw_display',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\_posix_raw_display.py',
   'PYMODULE'),
  ('urwid.display._raw_display_base',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\_raw_display_base.py',
   'PYMODULE'),
  ('urwid.display._win32',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\_win32.py',
   'PYMODULE'),
  ('urwid.display._win32_raw_display',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\_win32_raw_display.py',
   'PYMODULE'),
  ('urwid.display.common',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\common.py',
   'PYMODULE'),
  ('urwid.display.curses',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\curses.py',
   'PYMODULE'),
  ('urwid.display.escape',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\escape.py',
   'PYMODULE'),
  ('urwid.display.raw',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\raw.py',
   'PYMODULE'),
  ('urwid.event_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\__init__.py',
   'PYMODULE'),
  ('urwid.event_loop.abstract_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\abstract_loop.py',
   'PYMODULE'),
  ('urwid.event_loop.asyncio_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\asyncio_loop.py',
   'PYMODULE'),
  ('urwid.event_loop.glib_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\glib_loop.py',
   'PYMODULE'),
  ('urwid.event_loop.main_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\main_loop.py',
   'PYMODULE'),
  ('urwid.event_loop.select_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\select_loop.py',
   'PYMODULE'),
  ('urwid.event_loop.tornado_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\tornado_loop.py',
   'PYMODULE'),
  ('urwid.event_loop.trio_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\trio_loop.py',
   'PYMODULE'),
  ('urwid.event_loop.twisted_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\twisted_loop.py',
   'PYMODULE'),
  ('urwid.event_loop.zmq_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\zmq_loop.py',
   'PYMODULE'),
  ('urwid.font',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\font.py',
   'PYMODULE'),
  ('urwid.signals',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\signals.py',
   'PYMODULE'),
  ('urwid.split_repr',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\split_repr.py',
   'PYMODULE'),
  ('urwid.str_util',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\str_util.py',
   'PYMODULE'),
  ('urwid.text_layout',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\text_layout.py',
   'PYMODULE'),
  ('urwid.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\util.py',
   'PYMODULE'),
  ('urwid.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\version.py',
   'PYMODULE'),
  ('urwid.vterm',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\vterm.py',
   'PYMODULE'),
  ('urwid.widget',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\__init__.py',
   'PYMODULE'),
  ('urwid.widget.attr_map',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\attr_map.py',
   'PYMODULE'),
  ('urwid.widget.attr_wrap',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\attr_wrap.py',
   'PYMODULE'),
  ('urwid.widget.bar_graph',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\bar_graph.py',
   'PYMODULE'),
  ('urwid.widget.big_text',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\big_text.py',
   'PYMODULE'),
  ('urwid.widget.box_adapter',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\box_adapter.py',
   'PYMODULE'),
  ('urwid.widget.columns',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\columns.py',
   'PYMODULE'),
  ('urwid.widget.constants',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\constants.py',
   'PYMODULE'),
  ('urwid.widget.container',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\container.py',
   'PYMODULE'),
  ('urwid.widget.divider',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\divider.py',
   'PYMODULE'),
  ('urwid.widget.edit',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\edit.py',
   'PYMODULE'),
  ('urwid.widget.filler',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\filler.py',
   'PYMODULE'),
  ('urwid.widget.frame',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\frame.py',
   'PYMODULE'),
  ('urwid.widget.grid_flow',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\grid_flow.py',
   'PYMODULE'),
  ('urwid.widget.line_box',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\line_box.py',
   'PYMODULE'),
  ('urwid.widget.listbox',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\listbox.py',
   'PYMODULE'),
  ('urwid.widget.monitored_list',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\monitored_list.py',
   'PYMODULE'),
  ('urwid.widget.overlay',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\overlay.py',
   'PYMODULE'),
  ('urwid.widget.padding',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\padding.py',
   'PYMODULE'),
  ('urwid.widget.pile',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\pile.py',
   'PYMODULE'),
  ('urwid.widget.popup',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\popup.py',
   'PYMODULE'),
  ('urwid.widget.progress_bar',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\progress_bar.py',
   'PYMODULE'),
  ('urwid.widget.scrollable',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\scrollable.py',
   'PYMODULE'),
  ('urwid.widget.solid_fill',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\solid_fill.py',
   'PYMODULE'),
  ('urwid.widget.text',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\text.py',
   'PYMODULE'),
  ('urwid.widget.treetools',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\treetools.py',
   'PYMODULE'),
  ('urwid.widget.widget',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\widget.py',
   'PYMODULE'),
  ('urwid.widget.widget_decoration',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\widget_decoration.py',
   'PYMODULE'),
  ('urwid.widget.wimp',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\wimp.py',
   'PYMODULE'),
  ('uu', 'D:\\dev\\Python312\\Lib\\uu.py', 'PYMODULE'),
  ('uuid', 'D:\\dev\\Python312\\Lib\\uuid.py', 'PYMODULE'),
  ('wcwidth',
   'D:\\dev\\Python312\\Lib\\site-packages\\wcwidth\\__init__.py',
   'PYMODULE'),
  ('wcwidth.table_vs16',
   'D:\\dev\\Python312\\Lib\\site-packages\\wcwidth\\table_vs16.py',
   'PYMODULE'),
  ('wcwidth.table_wide',
   'D:\\dev\\Python312\\Lib\\site-packages\\wcwidth\\table_wide.py',
   'PYMODULE'),
  ('wcwidth.table_zero',
   'D:\\dev\\Python312\\Lib\\site-packages\\wcwidth\\table_zero.py',
   'PYMODULE'),
  ('wcwidth.unicode_versions',
   'D:\\dev\\Python312\\Lib\\site-packages\\wcwidth\\unicode_versions.py',
   'PYMODULE'),
  ('wcwidth.wcwidth',
   'D:\\dev\\Python312\\Lib\\site-packages\\wcwidth\\wcwidth.py',
   'PYMODULE'),
  ('webbrowser', 'D:\\dev\\Python312\\Lib\\webbrowser.py', 'PYMODULE'),
  ('werkzeug',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('win32_setctime',
   'D:\\dev\\Python312\\Lib\\site-packages\\win32_setctime\\__init__.py',
   'PYMODULE'),
  ('win32_setctime._setctime',
   'D:\\dev\\Python312\\Lib\\site-packages\\win32_setctime\\_setctime.py',
   'PYMODULE'),
  ('wsproto',
   'D:\\dev\\Python312\\Lib\\site-packages\\wsproto\\__init__.py',
   'PYMODULE'),
  ('wsproto.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\wsproto\\connection.py',
   'PYMODULE'),
  ('wsproto.events',
   'D:\\dev\\Python312\\Lib\\site-packages\\wsproto\\events.py',
   'PYMODULE'),
  ('wsproto.extensions',
   'D:\\dev\\Python312\\Lib\\site-packages\\wsproto\\extensions.py',
   'PYMODULE'),
  ('wsproto.frame_protocol',
   'D:\\dev\\Python312\\Lib\\site-packages\\wsproto\\frame_protocol.py',
   'PYMODULE'),
  ('wsproto.handshake',
   'D:\\dev\\Python312\\Lib\\site-packages\\wsproto\\handshake.py',
   'PYMODULE'),
  ('wsproto.typing',
   'D:\\dev\\Python312\\Lib\\site-packages\\wsproto\\typing.py',
   'PYMODULE'),
  ('wsproto.utilities',
   'D:\\dev\\Python312\\Lib\\site-packages\\wsproto\\utilities.py',
   'PYMODULE'),
  ('xml', 'D:\\dev\\Python312\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'D:\\dev\\Python312\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\dev\\Python312\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\dev\\Python312\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\dev\\Python312\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\dev\\Python312\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\dev\\Python312\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\dev\\Python312\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\dev\\Python312\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\dev\\Python312\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\dev\\Python312\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'D:\\dev\\Python312\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\dev\\Python312\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\dev\\Python312\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\dev\\Python312\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\dev\\Python312\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\dev\\Python312\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\dev\\Python312\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'D:\\dev\\Python312\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'D:\\dev\\Python312\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'D:\\dev\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\dev\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'D:\\dev\\Python312\\Lib\\zipimport.py', 'PYMODULE'),
  ('zstandard',
   'D:\\dev\\Python312\\Lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'D:\\dev\\Python312\\Lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE')])
