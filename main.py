#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
雀魂MAX独立版主启动文件
设计者：Nfilmjon (小约)
基于原作者Avenshy的思路进行独立设计和优化
用于PyInstaller打包的入口点
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 设置工作目录
os.chdir(current_dir)

def main():
    """主函数"""
    try:
        # 导入并运行addons模块
        import addons
        
        # 直接调用start_mitm函数
        asyncio.run(addons.start_mitm())
        
    except KeyboardInterrupt:
        print("\n程序已停止")
    except Exception as e:
        print(f"程序运行出错: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
