
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed), http.server (delayed, optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), test.support (delayed, conditional, optional)
missing module named urllib.unquote - imported by urllib (optional), ldap3.core.server (optional), ldap3.utils.uri (optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named annotationlib - imported by typing_extensions (conditional), attr._compat (conditional)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional), urwid.event_loop.main_loop (delayed, conditional), urwid.display._posix_raw_display (top-level), urwid.vterm (top-level), pty (delayed, optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level), loguru._logger (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named _scproxy - imported by urllib.request (conditional), future.backports.urllib.request (conditional)
missing module named termios - imported by getpass (optional), urwid.display.common (delayed, conditional), tty (top-level), urwid.display._posix_raw_display (top-level), urwid.vterm (top-level), werkzeug._reloader (delayed, optional), click._termui_impl (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.current_process - imported by multiprocessing (top-level), loguru._logger (top-level)
missing module named multiprocessing.Value - imported by multiprocessing (top-level), werkzeug.debug (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), flask.cli (delayed, conditional, optional), rlcompleter (optional), site (delayed, optional)
missing module named _typeshed - imported by werkzeug._internal (conditional), asgiref.sync (conditional), click.testing (conditional), setuptools._distutils.dist (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named pyimod02_importers - imported by D:\dev\Python312\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named google.protobuf.pyext._message - imported by google.protobuf.pyext (conditional), google.protobuf.descriptor (conditional), google.protobuf.pyext.cpp_message (top-level)
missing module named google.protobuf.enable_deterministic_proto_serialization - imported by google.protobuf (optional), google.protobuf.internal.api_implementation (optional)
missing module named google.protobuf.internal._api_implementation - imported by google.protobuf.internal (optional), google.protobuf.internal.api_implementation (optional)
missing module named aiocontextvars - imported by loguru._contextvars (delayed, conditional)
missing module named exceptiongroup - imported by urwid.event_loop.trio_loop (conditional), loguru._better_exceptions (conditional, optional)
missing module named IPython - imported by dotenv.ipython (top-level), loguru._colorama (delayed, conditional, optional)
missing module named ipykernel - imported by loguru._colorama (delayed, conditional, optional)
missing module named configobj - imported by ruamel.yaml.util (delayed)
missing module named ordereddict - imported by ruamel.yaml.compat (optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named dummy_threading - imported by requests.cookies (optional), future.backports.http.cookiejar (optional)
missing module named zstandard.backend_rust - imported by zstandard (conditional)
missing module named compression - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional), passlib.handlers.bcrypt (delayed, optional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level), urwid.display.curses (delayed, conditional)
missing module named win32evtlog - imported by logging.handlers (delayed, optional)
missing module named win32evtlogutil - imported by logging.handlers (delayed, optional)
missing module named zmq - imported by urwid.event_loop.zmq_loop (top-level)
missing module named trio - imported by urwid.event_loop.trio_loop (top-level)
missing module named gi - imported by urwid.event_loop.glib_loop (top-level)
missing module named 'twisted.internet' - imported by urwid.event_loop.twisted_loop (top-level)
missing module named twisted - imported by urwid.event_loop.twisted_loop (top-level)
missing module named 'backports.functools_lru_cache' - imported by wcwidth.wcwidth (optional)
missing module named 'mitmproxy_rs.syntax_highlight' - imported by mitmproxy.tools.console.flowview (top-level)
missing module named urllib2 - imported by publicsuffix2 (optional)
missing module named passlib.utils.compat.BytesIO - imported by passlib.utils.compat (top-level), passlib.context (top-level), passlib.crypto._blowfish (top-level), passlib.apache (top-level)
missing module named passlib.utils.compat.NativeStringIO - imported by passlib.utils.compat (top-level), passlib.context (top-level)
missing module named passlib.utils.compat.SafeConfigParser - imported by passlib.utils.compat (top-level), passlib.context (top-level)
missing module named __builtin__ - imported by future.utils (conditional), future.builtins.misc (conditional), future.builtins.new_min_max (conditional), passlib.utils.compat (conditional)
missing module named _crypt - imported by crypt (optional)
missing module named collections.Iterable - imported by collections (conditional), future.types.newbytes (conditional), future.types.newstr (conditional), future.types.newint (conditional), future.backports.http.client (conditional), future.backports.urllib.request (conditional), passlib.utils (optional)
missing module named collections.Sequence - imported by collections (optional), sortedcontainers.sortedlist (optional), sortedcontainers.sortedset (optional), sortedcontainers.sorteddict (optional), future.types.newrange (conditional), passlib.utils (optional)
missing module named scrypt - imported by passlib.crypto.scrypt (delayed, optional)
missing module named fastpbkdf2 - imported by passlib.crypto.digest (optional)
missing module named bcryptor - imported by passlib.handlers.bcrypt (delayed, optional)
missing module named 'bcrypt._bcrypt' - imported by passlib.handlers.bcrypt (delayed, optional)
missing module named argon2pure - imported by passlib.handlers.argon2 (delayed, optional)
missing module named collections.Mapping - imported by collections (optional), sortedcontainers.sorteddict (optional), future.backports.misc (conditional), ldap3.utils.ciDict (optional)
missing module named collections.MutableMapping - imported by collections (conditional), future.backports.misc (conditional), ldap3.utils.ciDict (optional)
missing module named 'pyasn1.compat.octets' - imported by ldap3.utils.asn1 (conditional)
missing module named UserDict - imported by ldap3.utils.ordDict (top-level)
missing module named 'backports.ssl_match_hostname' - imported by ldap3.utils.tls_backport (optional)
missing module named Crypto - imported by ldap3.utils.ntlm (delayed, conditional, optional)
missing module named winkerberos - imported by ldap3.protocol.sasl.kerberos (conditional, optional)
missing module named 'gssapi.raw' - imported by ldap3.protocol.sasl.kerberos (optional)
missing module named gssapi - imported by ldap3.protocol.sasl.kerberos (optional)
missing module named Queue - imported by ldap3.extend.microsoft.persistentSearch (optional), ldap3.extend.standard.PersistentSearch (optional), ldap3.strategy.reusable (optional), ldap3.strategy.asyncStream (optional)
missing module named _dbm - imported by dbm.ndbm (top-level)
missing module named gdbm - imported by anydbm (top-level), future.moves.dbm.gnu (conditional)
missing module named _gdbm - imported by dbm.gnu (top-level)
missing module named dumbdbm - imported by anydbm (top-level), future.moves.dbm.dumb (conditional)
missing module named anydbm - imported by future.moves.dbm (conditional)
missing module named dbhash - imported by anydbm (top-level)
missing module named whichdb - imported by future.moves.dbm (conditional), anydbm (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named dummy_thread - imported by sortedcontainers.sortedlist (conditional, optional), future.backports.misc (conditional, optional)
missing module named thread - imported by sortedcontainers.sortedlist (conditional, optional), future.backports.misc (conditional, optional)
missing module named _dummy_thread - imported by sortedcontainers.sortedlist (conditional, optional), future.backports.misc (conditional, optional)
missing module named collections.Iterator - imported by collections (conditional), future.types.newrange (conditional)
missing module named future_builtins - imported by future.builtins.misc (conditional)
missing module named imp - imported by future.standard_library (conditional)
missing module named 'watchdog.observers' - imported by werkzeug._reloader (delayed)
missing module named 'watchdog.events' - imported by werkzeug._reloader (delayed)
missing module named watchdog - imported by werkzeug._reloader (delayed)
missing module named '_typeshed.wsgi' - imported by werkzeug.exceptions (conditional), werkzeug.http (conditional), werkzeug.wsgi (conditional), werkzeug.utils (conditional), werkzeug.wrappers.response (conditional), werkzeug.test (conditional), werkzeug.datastructures.headers (conditional), werkzeug.formparser (conditional), werkzeug.wrappers.request (conditional), werkzeug.serving (conditional), werkzeug.debug (conditional), werkzeug.middleware.shared_data (conditional), werkzeug.local (conditional), werkzeug.routing.exceptions (conditional), werkzeug.routing.map (conditional), flask.typing (conditional), flask.ctx (conditional), flask.testing (conditional), flask.cli (conditional), flask.app (conditional)
missing module named 'IPython.core' - imported by dotenv.ipython (top-level)
missing module named AppKit - imported by pyperclip (delayed, conditional, optional)
missing module named Foundation - imported by pyperclip (delayed, conditional, optional)
missing module named PyQt5 - imported by pyperclip (delayed, conditional, optional)
missing module named qtpy - imported by pyperclip (delayed, conditional, optional)
missing module named collections.ValuesView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.KeysView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.ItemsView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.Set - imported by collections (optional), sortedcontainers.sortedset (optional)
missing module named collections.MutableSet - imported by collections (optional), sortedcontainers.sortedset (optional)
missing module named collections.MutableSequence - imported by collections (optional), sortedcontainers.sortedlist (optional)
missing module named win_inet_pton - imported by pydivert (conditional)
missing module named 'mitmproxy_rs.contentviews' - imported by mitmproxy.contentviews._view_http3 (top-level), mitmproxy.contentviews (top-level)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
