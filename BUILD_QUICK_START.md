# 雀魂MAX独立版快速编译指南

**设计者：Nfilmjon (小约)**
**版本：20250801**
**基于原作者Avenshy的思路进行独立设计和优化**

## 🚀 一键编译

### 方法1: 使用Python脚本（推荐）
```bash
python build.py
```

### 方法2: 使用英文批处理文件
```bash
build_en.bat
```

### 方法3: 使用中文批处理文件
```bash
build.bat
```

## ❌ 如果遇到编码错误

你遇到的错误是因为批处理文件中的中文字符编码问题。解决方案：

1. **使用Python脚本**（最佳选择）:
   ```bash
   python build.py
   ```

2. **使用英文版批处理文件**:
   ```bash
   build_en.bat
   ```

3. **手动命令**（如果上述都不行）:
   ```bash
   # 安装依赖
   pip install -r requirements_fixed.txt
   pip install pyinstaller
   
   # 编译
   pyinstaller --onefile --console --name=MajsoulMax --add-data="config;config" --add-data="proto;proto" --add-data="plugin;plugin" --hidden-import=mitmproxy.tools.dump --hidden-import=mitmproxy.options --hidden-import=mitmproxy.http --hidden-import=mitmproxy.ctx --hidden-import=liqi_new --hidden-import=plugin.helper --hidden-import=plugin.mod --hidden-import=plugin.update_liqi --hidden-import=proto.liqi_pb2 --hidden-import=proto.config_pb2 --hidden-import=proto.sheets_pb2 --hidden-import=proto.basic_pb2 --hidden-import=ruamel.yaml --hidden-import=loguru --hidden-import=google.protobuf.json_format main.py
   ```

## 📁 编译后的文件

- **位置**: `dist/MajsoulMax.exe`
- **大小**: 约50-100MB
- **功能**: 等同于 `mitmdump -p 23410 -s addons.py`
- **版本**: 雀魂MAX独立版 v20250801 by Nfilmjon (小约)

## 🎯 使用编译后的exe

1. 双击 `MajsoulMax.exe` 启动
2. 程序会显示控制台窗口（不要关闭）
3. 按照提示配置浏览器或游戏客户端
4. 首次运行需要网络连接下载协议文件

## 🔧 故障排除

### 编码错误
- 使用 `python build.py` 或 `build_en.bat`

### Python版本错误
- 确保Python版本 >= 3.10

### 依赖安装失败
- 检查网络连接
- 尝试使用 `pip install -r requirements_fixed.txt`

### 杀毒软件误报
- 将exe文件添加到杀毒软件白名单

## 📋 完整文件列表

编译相关文件：
- `build.py` - Python编译脚本（推荐）
- `build_en.bat` - 英文批处理脚本
- `build.bat` - 中文批处理脚本（可能有编码问题）
- `main.py` - PyInstaller入口文件
- `requirements_fixed.txt` - 修复后的依赖文件

## 💡 技术说明

编译过程：
1. 安装Python依赖包
2. 使用PyInstaller打包
3. 将所有依赖和数据文件打包成单个exe
4. 生成可独立运行的程序

原理：
- exe文件包含完整的Python运行环境
- 运行时解压到临时目录
- 启动内置Python解释器执行代码
- 实现与原始mitmdump命令相同的功能
