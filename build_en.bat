@echo off
title MajsoulMax Independent Edition Build Tool

echo.
echo ================================================
echo     MajsoulMax Independent Edition Build Tool
echo     Designer: Nfilmjon (<PERSON>)
echo ================================================
echo Based on original author <PERSON><PERSON><PERSON>'s ideas
echo Independently designed and optimized
echo.

:: Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.10 or higher
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: Check if we're in the correct directory
if not exist "addons.py" (
    echo ERROR: Please run this script in the project root directory
    echo Make sure addons.py exists in current directory
    pause
    exit /b 1
)

echo Installing dependencies...
:: Try to use fixed requirements file first
if exist "requirements_fixed.txt" (
    echo Using fixed requirements file...
    pip install -r requirements_fixed.txt
) else (
    echo Using original requirements file...
    pip install -r requirements.txt
)
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Installing PyInstaller...
pip install pyinstaller
if errorlevel 1 (
    echo ERROR: Failed to install PyInstaller
    pause
    exit /b 1
)

echo.
echo Starting build process...
echo This may take several minutes, please wait...
echo.

pyinstaller ^
    --onefile ^
    --console ^
    --name=MajsoulMax ^
    --add-data="config;config" ^
    --add-data="proto;proto" ^
    --add-data="plugin;plugin" ^
    --hidden-import=mitmproxy.tools.dump ^
    --hidden-import=mitmproxy.options ^
    --hidden-import=mitmproxy.http ^
    --hidden-import=mitmproxy.ctx ^
    --hidden-import=liqi_new ^
    --hidden-import=plugin.helper ^
    --hidden-import=plugin.mod ^
    --hidden-import=plugin.update_liqi ^
    --hidden-import=proto.liqi_pb2 ^
    --hidden-import=proto.config_pb2 ^
    --hidden-import=proto.sheets_pb2 ^
    --hidden-import=proto.basic_pb2 ^
    --hidden-import=ruamel.yaml ^
    --hidden-import=loguru ^
    --hidden-import=google.protobuf.json_format ^
    main.py

if errorlevel 1 (
    echo.
    echo ERROR: Build failed!
    echo Please check the error messages above
    pause
    exit /b 1
)

echo.
echo ================================================
echo    MajsoulMax Independent Edition Build Complete!
echo    Designer: Nfilmjon (Xiao Yue)
echo ================================================
echo.
echo Output file: dist\MajsoulMax.exe
echo.
echo Usage:
echo 1. Copy dist\MajsoulMax.exe to any location
echo 2. Double-click to run (equivalent to: mitmdump -p 23410 -s addons.py)
echo 3. Configure your browser or game client as instructed
echo.
echo Notes:
echo - First run requires internet connection
echo - Some antivirus may flag the exe, add to whitelist if needed
echo - Keep the console window open while running
echo.
echo Build completed! Press any key to exit...
pause >nul
