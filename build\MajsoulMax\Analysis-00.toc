(['D:\\alone\\2.0\\MajsoulMax-main\\main.py'],
 ['D:\\alone\\2.0\\MajsoulMax-main'],
 ['mitmproxy.tools.dump',
  'mitmproxy.options',
  'mitmproxy.http',
  'mitmproxy.ctx',
  'liqi_new',
  'plugin.helper',
  'plugin.mod',
  'plugin.update_liqi',
  'proto.liqi_pb2',
  'proto.config_pb2',
  'proto.sheets_pb2',
  'proto.basic_pb2',
  'ruamel.yaml',
  'loguru',
  'google.protobuf.json_format'],
 [('D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\pyinstaller', 0),
  ('D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_rs\\_pyinstaller', 0),
  ('D:\\dev\\Python312\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\dev\\Python312\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\dev\\Python312\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('config\\.gitkeep',
   'D:\\alone\\2.0\\MajsoulMax-main\\config\\.gitkeep',
   'DATA'),
  ('config\\settings.mod.yaml',
   'D:\\alone\\2.0\\MajsoulMax-main\\config\\settings.mod.yaml',
   'DATA'),
  ('config\\settings.yaml',
   'D:\\alone\\2.0\\MajsoulMax-main\\config\\settings.yaml',
   'DATA'),
  ('plugin\\helper.py',
   'D:\\alone\\2.0\\MajsoulMax-main\\plugin\\helper.py',
   'DATA'),
  ('plugin\\mod.py', 'D:\\alone\\2.0\\MajsoulMax-main\\plugin\\mod.py', 'DATA'),
  ('plugin\\update_liqi.py',
   'D:\\alone\\2.0\\MajsoulMax-main\\plugin\\update_liqi.py',
   'DATA'),
  ('proto\\basic.proto',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\basic.proto',
   'DATA'),
  ('proto\\basic_pb2.py',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\basic_pb2.py',
   'DATA'),
  ('proto\\config_pb2.py',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\config_pb2.py',
   'DATA'),
  ('proto\\liqi.json',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\liqi.json',
   'DATA'),
  ('proto\\liqi.proto',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\liqi.proto',
   'DATA'),
  ('proto\\liqi_pb2.py',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\liqi_pb2.py',
   'DATA'),
  ('proto\\lqc.lqbin',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\lqc.lqbin',
   'DATA'),
  ('proto\\sheets_pb2.py',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\sheets_pb2.py',
   'DATA')],
 '3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'D:\\dev\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\dev\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\dev\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\dev\\Python312\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\dev\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\dev\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main', 'D:\\alone\\2.0\\MajsoulMax-main\\main.py', 'PYSOURCE')],
 [('_distutils_hack',
   'D:\\dev\\Python312\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\dev\\Python312\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('threading', 'D:\\dev\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\dev\\Python312\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('contextlib', 'D:\\dev\\Python312\\Lib\\contextlib.py', 'PYMODULE'),
  ('_strptime', 'D:\\dev\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('datetime', 'D:\\dev\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\dev\\Python312\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('calendar', 'D:\\dev\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'D:\\dev\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\dev\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('shutil', 'D:\\dev\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'D:\\dev\\Python312\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'D:\\dev\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\dev\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile', 'D:\\dev\\Python312\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\dev\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('struct', 'D:\\dev\\Python312\\Lib\\struct.py', 'PYMODULE'),
  ('tarfile', 'D:\\dev\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\dev\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'D:\\dev\\Python312\\Lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'D:\\dev\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\dev\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'D:\\dev\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('copy', 'D:\\dev\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\dev\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\dev\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing', 'D:\\dev\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\dev\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'D:\\dev\\Python312\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\dev\\Python312\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\dev\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\dev\\Python312\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'D:\\dev\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\dev\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\dev\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'D:\\dev\\Python312\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators',
   'D:\\dev\\Python312\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\dev\\Python312\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('random', 'D:\\dev\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\dev\\Python312\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\dev\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\dev\\Python312\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\dev\\Python312\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\dev\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\dev\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\dev\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'D:\\dev\\Python312\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'D:\\dev\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\dev\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\dev\\Python312\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\dev\\Python312\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('bisect', 'D:\\dev\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\dev\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\dev\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\dev\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'D:\\dev\\Python312\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'D:\\dev\\Python312\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime',
   'D:\\dev\\Python312\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\dev\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'D:\\dev\\Python312\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'D:\\dev\\Python312\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'D:\\dev\\Python312\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr',
   'D:\\dev\\Python312\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('urllib.parse', 'D:\\dev\\Python312\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'D:\\dev\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('socket', 'D:\\dev\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\dev\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('quopri', 'D:\\dev\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('inspect', 'D:\\dev\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('token', 'D:\\dev\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('dis', 'D:\\dev\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\dev\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\dev\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('email', 'D:\\dev\\Python312\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\dev\\Python312\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser',
   'D:\\dev\\Python312\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'D:\\dev\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\dev\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\dev\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'D:\\dev\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('tokenize', 'D:\\dev\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\dev\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc', 'D:\\dev\\Python312\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.abc', 'D:\\dev\\Python312\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib', 'D:\\dev\\Python312\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('setuptools',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\dev\\Python312\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\dev\\Python312\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('subprocess', 'D:\\dev\\Python312\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'D:\\dev\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('unittest.mock', 'D:\\dev\\Python312\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest', 'D:\\dev\\Python312\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\dev\\Python312\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\dev\\Python312\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main', 'D:\\dev\\Python312\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner',
   'D:\\dev\\Python312\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\dev\\Python312\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite', 'D:\\dev\\Python312\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.case', 'D:\\dev\\Python312\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'D:\\dev\\Python312\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('difflib', 'D:\\dev\\Python312\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result',
   'D:\\dev\\Python312\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util', 'D:\\dev\\Python312\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('pkgutil', 'D:\\dev\\Python312\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\dev\\Python312\\Lib\\zipimport.py', 'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('sysconfig', 'D:\\dev\\Python312\\Lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support', 'D:\\dev\\Python312\\Lib\\_aix_support.py', 'PYMODULE'),
  ('setuptools._distutils',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('queue', 'D:\\dev\\Python312\\Lib\\queue.py', 'PYMODULE'),
  ('platform', 'D:\\dev\\Python312\\Lib\\platform.py', 'PYMODULE'),
  ('json', 'D:\\dev\\Python312\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'D:\\dev\\Python312\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'D:\\dev\\Python312\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\dev\\Python312\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\dev\\Python312\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\dev\\Python312\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\dev\\Python312\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'D:\\dev\\Python312\\Lib\\runpy.py', 'PYMODULE'),
  ('multiprocessing.util',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('ctypes', 'D:\\dev\\Python312\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\dev\\Python312\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\dev\\Python312\\Lib\\secrets.py', 'PYMODULE'),
  ('hmac', 'D:\\dev\\Python312\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client', 'D:\\dev\\Python312\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\dev\\Python312\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\dev\\Python312\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\dev\\Python312\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\dev\\Python312\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\dev\\Python312\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\dev\\Python312\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request', 'D:\\dev\\Python312\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'D:\\dev\\Python312\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\dev\\Python312\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\dev\\Python312\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\dev\\Python312\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'D:\\dev\\Python312\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\dev\\Python312\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'D:\\dev\\Python312\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'D:\\dev\\Python312\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\dev\\Python312\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error', 'D:\\dev\\Python312\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('xml.sax', 'D:\\dev\\Python312\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler',
   'D:\\dev\\Python312\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\dev\\Python312\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\dev\\Python312\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client', 'D:\\dev\\Python312\\Lib\\http\\client.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\dev\\Python312\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\dev\\Python312\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\dev\\Python312\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('site', 'D:\\dev\\Python312\\Lib\\site.py', 'PYMODULE'),
  ('rlcompleter', 'D:\\dev\\Python312\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', 'D:\\dev\\Python312\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', 'D:\\dev\\Python312\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'D:\\dev\\Python312\\Lib\\webbrowser.py', 'PYMODULE'),
  ('shlex', 'D:\\dev\\Python312\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server', 'D:\\dev\\Python312\\Lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'D:\\dev\\Python312\\Lib\\socketserver.py', 'PYMODULE'),
  ('html', 'D:\\dev\\Python312\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\dev\\Python312\\Lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\dev\\Python312\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\dev\\Python312\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'D:\\dev\\Python312\\Lib\\tty.py', 'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser', 'D:\\dev\\Python312\\Lib\\configparser.py', 'PYMODULE'),
  ('packaging.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib', 'D:\\dev\\Python312\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser',
   'D:\\dev\\Python312\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._types', 'D:\\dev\\Python312\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tomllib._re', 'D:\\dev\\Python312\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\dev\\Python312\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('glob', 'D:\\dev\\Python312\\Lib\\glob.py', 'PYMODULE'),
  ('setuptools._shutil',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\dev\\Python312\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\dev\\Python312\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('__future__', 'D:\\dev\\Python312\\Lib\\__future__.py', 'PYMODULE'),
  ('google.protobuf.json_format',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\json_format.py',
   'PYMODULE'),
  ('google.protobuf.symbol_database',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\symbol_database.py',
   'PYMODULE'),
  ('google.protobuf.message_factory',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\message_factory.py',
   'PYMODULE'),
  ('google.protobuf.internal.python_message',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\python_message.py',
   'PYMODULE'),
  ('google.protobuf.text_format',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\text_format.py',
   'PYMODULE'),
  ('google.protobuf.text_encoding',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\text_encoding.py',
   'PYMODULE'),
  ('google.protobuf.internal.wire_format',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\wire_format.py',
   'PYMODULE'),
  ('google.protobuf.internal.well_known_types',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\well_known_types.py',
   'PYMODULE'),
  ('google.protobuf.internal.message_listener',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\message_listener.py',
   'PYMODULE'),
  ('google.protobuf.internal.extension_dict',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\extension_dict.py',
   'PYMODULE'),
  ('google.protobuf.internal.enum_type_wrapper',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\enum_type_wrapper.py',
   'PYMODULE'),
  ('google.protobuf.internal.encoder',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\encoder.py',
   'PYMODULE'),
  ('google.protobuf.internal.decoder',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\decoder.py',
   'PYMODULE'),
  ('google.protobuf.internal.containers',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\containers.py',
   'PYMODULE'),
  ('google.protobuf.pyext.cpp_message',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\pyext\\cpp_message.py',
   'PYMODULE'),
  ('google.protobuf.pyext',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\pyext\\__init__.py',
   'PYMODULE'),
  ('google.protobuf.message',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\message.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_pool',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\descriptor_pool.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_pb2',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\descriptor_pb2.py',
   'PYMODULE'),
  ('google.protobuf.internal.builder',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\builder.py',
   'PYMODULE'),
  ('google.protobuf.service_reflection',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\service_reflection.py',
   'PYMODULE'),
  ('google.protobuf.service',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\service.py',
   'PYMODULE'),
  ('google.protobuf.reflection',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\reflection.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_database',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\descriptor_database.py',
   'PYMODULE'),
  ('google.protobuf.internal.api_implementation',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\api_implementation.py',
   'PYMODULE'),
  ('google.protobuf.descriptor',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\descriptor.py',
   'PYMODULE'),
  ('google.protobuf',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\__init__.py',
   'PYMODULE'),
  ('google', '-', 'PYMODULE'),
  ('google.protobuf.internal.type_checkers',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\type_checkers.py',
   'PYMODULE'),
  ('google.protobuf.internal',
   'D:\\dev\\Python312\\Lib\\site-packages\\google\\protobuf\\internal\\__init__.py',
   'PYMODULE'),
  ('loguru',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\__init__.py',
   'PYMODULE'),
  ('loguru._logger',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_logger.py',
   'PYMODULE'),
  ('loguru._simple_sinks',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_simple_sinks.py',
   'PYMODULE'),
  ('loguru._recattrs',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_recattrs.py',
   'PYMODULE'),
  ('loguru._locks_machinery',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_locks_machinery.py',
   'PYMODULE'),
  ('loguru._handler',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_handler.py',
   'PYMODULE'),
  ('loguru._get_frame',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_get_frame.py',
   'PYMODULE'),
  ('loguru._file_sink',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_file_sink.py',
   'PYMODULE'),
  ('loguru._ctime_functions',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_ctime_functions.py',
   'PYMODULE'),
  ('win32_setctime',
   'D:\\dev\\Python312\\Lib\\site-packages\\win32_setctime\\__init__.py',
   'PYMODULE'),
  ('win32_setctime._setctime',
   'D:\\dev\\Python312\\Lib\\site-packages\\win32_setctime\\_setctime.py',
   'PYMODULE'),
  ('loguru._error_interceptor',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_error_interceptor.py',
   'PYMODULE'),
  ('loguru._datetime',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_datetime.py',
   'PYMODULE'),
  ('loguru._contextvars',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_contextvars.py',
   'PYMODULE'),
  ('loguru._colorizer',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_colorizer.py',
   'PYMODULE'),
  ('loguru._better_exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_better_exceptions.py',
   'PYMODULE'),
  ('loguru._string_parsers',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_string_parsers.py',
   'PYMODULE'),
  ('loguru._filters',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_filters.py',
   'PYMODULE'),
  ('loguru._colorama',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_colorama.py',
   'PYMODULE'),
  ('colorama',
   'D:\\dev\\Python312\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\dev\\Python312\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\dev\\Python312\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\dev\\Python312\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\dev\\Python312\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\dev\\Python312\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('loguru._asyncio_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_asyncio_loop.py',
   'PYMODULE'),
  ('loguru._defaults',
   'D:\\dev\\Python312\\Lib\\site-packages\\loguru\\_defaults.py',
   'PYMODULE'),
  ('ruamel.yaml',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\__init__.py',
   'PYMODULE'),
  ('ruamel.yaml.main',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\main.py',
   'PYMODULE'),
  ('ruamel.yaml.docinfo',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\docinfo.py',
   'PYMODULE'),
  ('ruamel.yaml.comments',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\comments.py',
   'PYMODULE'),
  ('ruamel.yaml.tag',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\tag.py',
   'PYMODULE'),
  ('ruamel.yaml.anchor',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\anchor.py',
   'PYMODULE'),
  ('ruamel.yaml.scalarstring',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\scalarstring.py',
   'PYMODULE'),
  ('ruamel.yaml.constructor',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\constructor.py',
   'PYMODULE'),
  ('ruamel.yaml.serializer',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\serializer.py',
   'PYMODULE'),
  ('ruamel.yaml.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\util.py',
   'PYMODULE'),
  ('ruamel.yaml.timestamp',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\timestamp.py',
   'PYMODULE'),
  ('ruamel.yaml.scalarbool',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\scalarbool.py',
   'PYMODULE'),
  ('ruamel.yaml.scalarfloat',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\scalarfloat.py',
   'PYMODULE'),
  ('ruamel.yaml.scalarint',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\scalarint.py',
   'PYMODULE'),
  ('ruamel.yaml.representer',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\representer.py',
   'PYMODULE'),
  ('ruamel.yaml.resolver',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\resolver.py',
   'PYMODULE'),
  ('ruamel.yaml.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\compat.py',
   'PYMODULE'),
  ('ruamel.yaml.dumper',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\dumper.py',
   'PYMODULE'),
  ('ruamel.yaml.emitter',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\emitter.py',
   'PYMODULE'),
  ('ruamel.yaml.loader',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\loader.py',
   'PYMODULE'),
  ('ruamel.yaml.composer',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\composer.py',
   'PYMODULE'),
  ('ruamel.yaml.parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\parser.py',
   'PYMODULE'),
  ('ruamel.yaml.scanner',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\scanner.py',
   'PYMODULE'),
  ('ruamel.yaml.reader',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\reader.py',
   'PYMODULE'),
  ('ruamel.yaml.nodes',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\nodes.py',
   'PYMODULE'),
  ('ruamel.yaml.events',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\events.py',
   'PYMODULE'),
  ('ruamel.yaml.tokens',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\tokens.py',
   'PYMODULE'),
  ('ruamel.yaml.error',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\error.py',
   'PYMODULE'),
  ('ruamel.yaml.cyaml',
   'D:\\dev\\Python312\\Lib\\site-packages\\ruamel\\yaml\\cyaml.py',
   'PYMODULE'),
  ('ruamel', '-', 'PYMODULE'),
  ('proto.basic_pb2',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\basic_pb2.py',
   'PYMODULE'),
  ('proto', '-', 'PYMODULE'),
  ('proto.sheets_pb2',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\sheets_pb2.py',
   'PYMODULE'),
  ('proto.config_pb2',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\config_pb2.py',
   'PYMODULE'),
  ('proto.liqi_pb2',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\liqi_pb2.py',
   'PYMODULE'),
  ('plugin.update_liqi',
   'D:\\alone\\2.0\\MajsoulMax-main\\plugin\\update_liqi.py',
   'PYMODULE'),
  ('plugin', '-', 'PYMODULE'),
  ('requests',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies', 'D:\\dev\\Python312\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('requests.models',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'D:\\dev\\Python312\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\dev\\Python312\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\dev\\Python312\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\dev\\Python312\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\dev\\Python312\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\dev\\Python312\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('zstandard',
   'D:\\dev\\Python312\\Lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'D:\\dev\\Python312\\Lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('h2.events',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\events.py',
   'PYMODULE'),
  ('h2', 'D:\\dev\\Python312\\Lib\\site-packages\\h2\\__init__.py', 'PYMODULE'),
  ('h2.settings',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\settings.py',
   'PYMODULE'),
  ('h2.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\exceptions.py',
   'PYMODULE'),
  ('h2.errors',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\errors.py',
   'PYMODULE'),
  ('hyperframe.frame',
   'D:\\dev\\Python312\\Lib\\site-packages\\hyperframe\\frame.py',
   'PYMODULE'),
  ('hyperframe',
   'D:\\dev\\Python312\\Lib\\site-packages\\hyperframe\\__init__.py',
   'PYMODULE'),
  ('hyperframe.flags',
   'D:\\dev\\Python312\\Lib\\site-packages\\hyperframe\\flags.py',
   'PYMODULE'),
  ('hyperframe.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\hyperframe\\exceptions.py',
   'PYMODULE'),
  ('h2.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\connection.py',
   'PYMODULE'),
  ('h2.windows',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\windows.py',
   'PYMODULE'),
  ('h2.utilities',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\utilities.py',
   'PYMODULE'),
  ('hpack',
   'D:\\dev\\Python312\\Lib\\site-packages\\hpack\\__init__.py',
   'PYMODULE'),
  ('hpack.struct',
   'D:\\dev\\Python312\\Lib\\site-packages\\hpack\\struct.py',
   'PYMODULE'),
  ('h2.stream',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\stream.py',
   'PYMODULE'),
  ('h2.frame_buffer',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\frame_buffer.py',
   'PYMODULE'),
  ('hpack.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\hpack\\exceptions.py',
   'PYMODULE'),
  ('hpack.hpack',
   'D:\\dev\\Python312\\Lib\\site-packages\\hpack\\hpack.py',
   'PYMODULE'),
  ('hpack.table',
   'D:\\dev\\Python312\\Lib\\site-packages\\hpack\\table.py',
   'PYMODULE'),
  ('hpack.huffman_table',
   'D:\\dev\\Python312\\Lib\\site-packages\\hpack\\huffman_table.py',
   'PYMODULE'),
  ('hpack.huffman_constants',
   'D:\\dev\\Python312\\Lib\\site-packages\\hpack\\huffman_constants.py',
   'PYMODULE'),
  ('hpack.huffman',
   'D:\\dev\\Python312\\Lib\\site-packages\\hpack\\huffman.py',
   'PYMODULE'),
  ('h2.config',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2\\config.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('brotli', 'D:\\dev\\Python312\\Lib\\site-packages\\brotli.py', 'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'D:\\dev\\Python312\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\dev\\Python312\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('cryptography',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hmac',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'D:\\dev\\Python312\\Lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL',
   'D:\\dev\\Python312\\Lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'D:\\dev\\Python312\\Lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'D:\\dev\\Python312\\Lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('chardet',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.escprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.enums',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'D:\\dev\\Python312\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\dev\\Python312\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('plugin.mod', 'D:\\alone\\2.0\\MajsoulMax-main\\plugin\\mod.py', 'PYMODULE'),
  ('plugin.helper',
   'D:\\alone\\2.0\\MajsoulMax-main\\plugin\\helper.py',
   'PYMODULE'),
  ('liqi_new', 'D:\\alone\\2.0\\MajsoulMax-main\\liqi_new.py', 'PYMODULE'),
  ('mitmproxy.ctx',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\ctx.py',
   'PYMODULE'),
  ('mitmproxy',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.script',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\script\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.script.concurrent',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\script\\concurrent.py',
   'PYMODULE'),
  ('mitmproxy.addons',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.addons.keepserving',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\keepserving.py',
   'PYMODULE'),
  ('mitmproxy.utils.asyncio_utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\asyncio_utils.py',
   'PYMODULE'),
  ('mitmproxy.utils.human',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\human.py',
   'PYMODULE'),
  ('mitmproxy.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.utils.emoji',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\emoji.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.common',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\common.py',
   'PYMODULE'),
  ('mitmproxy.tools.console',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.master',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\master.py',
   'PYMODULE'),
  ('tornado.platform.asyncio',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\platform\\asyncio.py',
   'PYMODULE'),
  ('tornado.platform',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\platform\\__init__.py',
   'PYMODULE'),
  ('tornado',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\__init__.py',
   'PYMODULE'),
  ('tornado.ioloop',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\ioloop.py',
   'PYMODULE'),
  ('tornado.process',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\process.py',
   'PYMODULE'),
  ('tornado.iostream',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\iostream.py',
   'PYMODULE'),
  ('doctest', 'D:\\dev\\Python312\\Lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'D:\\dev\\Python312\\Lib\\pdb.py', 'PYMODULE'),
  ('code', 'D:\\dev\\Python312\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\dev\\Python312\\Lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'D:\\dev\\Python312\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\dev\\Python312\\Lib\\cmd.py', 'PYMODULE'),
  ('tornado.netutil',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\netutil.py',
   'PYMODULE'),
  ('tornado.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\util.py',
   'PYMODULE'),
  ('tornado.log',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\log.py',
   'PYMODULE'),
  ('tornado.options',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\options.py',
   'PYMODULE'),
  ('curses', 'D:\\dev\\Python312\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'D:\\dev\\Python312\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('tornado.escape',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\escape.py',
   'PYMODULE'),
  ('logging.handlers',
   'D:\\dev\\Python312\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('smtplib', 'D:\\dev\\Python312\\Lib\\smtplib.py', 'PYMODULE'),
  ('tornado.concurrent',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\concurrent.py',
   'PYMODULE'),
  ('tornado.gen',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\gen.py',
   'PYMODULE'),
  ('urwid',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\__init__.py',
   'PYMODULE'),
  ('urwid.vterm',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\vterm.py',
   'PYMODULE'),
  ('urwid.display.common',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\common.py',
   'PYMODULE'),
  ('urwid.display.escape',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\escape.py',
   'PYMODULE'),
  ('pty', 'D:\\dev\\Python312\\Lib\\pty.py', 'PYMODULE'),
  ('urwid.widget',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\__init__.py',
   'PYMODULE'),
  ('urwid.widget.wimp',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\wimp.py',
   'PYMODULE'),
  ('urwid.widget.widget_decoration',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\widget_decoration.py',
   'PYMODULE'),
  ('urwid.widget.widget',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\widget.py',
   'PYMODULE'),
  ('urwid.split_repr',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\split_repr.py',
   'PYMODULE'),
  ('urwid.widget.treetools',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\treetools.py',
   'PYMODULE'),
  ('urwid.widget.text',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\text.py',
   'PYMODULE'),
  ('urwid.widget.solid_fill',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\solid_fill.py',
   'PYMODULE'),
  ('urwid.widget.scrollable',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\scrollable.py',
   'PYMODULE'),
  ('urwid.widget.progress_bar',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\progress_bar.py',
   'PYMODULE'),
  ('urwid.widget.popup',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\popup.py',
   'PYMODULE'),
  ('urwid.widget.pile',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\pile.py',
   'PYMODULE'),
  ('urwid.widget.padding',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\padding.py',
   'PYMODULE'),
  ('urwid.widget.overlay',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\overlay.py',
   'PYMODULE'),
  ('urwid.widget.monitored_list',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\monitored_list.py',
   'PYMODULE'),
  ('urwid.widget.listbox',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\listbox.py',
   'PYMODULE'),
  ('urwid.widget.line_box',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\line_box.py',
   'PYMODULE'),
  ('urwid.widget.grid_flow',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\grid_flow.py',
   'PYMODULE'),
  ('urwid.widget.frame',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\frame.py',
   'PYMODULE'),
  ('urwid.widget.filler',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\filler.py',
   'PYMODULE'),
  ('urwid.widget.edit',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\edit.py',
   'PYMODULE'),
  ('urwid.widget.divider',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\divider.py',
   'PYMODULE'),
  ('urwid.widget.container',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\container.py',
   'PYMODULE'),
  ('urwid.widget.constants',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\constants.py',
   'PYMODULE'),
  ('urwid.widget.columns',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\columns.py',
   'PYMODULE'),
  ('urwid.widget.box_adapter',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\box_adapter.py',
   'PYMODULE'),
  ('urwid.widget.big_text',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\big_text.py',
   'PYMODULE'),
  ('urwid.widget.bar_graph',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\bar_graph.py',
   'PYMODULE'),
  ('urwid.widget.attr_wrap',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\attr_wrap.py',
   'PYMODULE'),
  ('urwid.widget.attr_map',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\widget\\attr_map.py',
   'PYMODULE'),
  ('urwid.event_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\__init__.py',
   'PYMODULE'),
  ('urwid.event_loop.zmq_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\zmq_loop.py',
   'PYMODULE'),
  ('urwid.event_loop.trio_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\trio_loop.py',
   'PYMODULE'),
  ('urwid.event_loop.glib_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\glib_loop.py',
   'PYMODULE'),
  ('urwid.event_loop.tornado_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\tornado_loop.py',
   'PYMODULE'),
  ('urwid.event_loop.twisted_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\twisted_loop.py',
   'PYMODULE'),
  ('urwid.event_loop.select_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\select_loop.py',
   'PYMODULE'),
  ('urwid.event_loop.main_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\main_loop.py',
   'PYMODULE'),
  ('urwid.event_loop.asyncio_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\asyncio_loop.py',
   'PYMODULE'),
  ('urwid.event_loop.abstract_loop',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\event_loop\\abstract_loop.py',
   'PYMODULE'),
  ('urwid.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\version.py',
   'PYMODULE'),
  ('urwid.str_util',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\str_util.py',
   'PYMODULE'),
  ('wcwidth',
   'D:\\dev\\Python312\\Lib\\site-packages\\wcwidth\\__init__.py',
   'PYMODULE'),
  ('wcwidth.wcwidth',
   'D:\\dev\\Python312\\Lib\\site-packages\\wcwidth\\wcwidth.py',
   'PYMODULE'),
  ('wcwidth.unicode_versions',
   'D:\\dev\\Python312\\Lib\\site-packages\\wcwidth\\unicode_versions.py',
   'PYMODULE'),
  ('wcwidth.table_zero',
   'D:\\dev\\Python312\\Lib\\site-packages\\wcwidth\\table_zero.py',
   'PYMODULE'),
  ('wcwidth.table_wide',
   'D:\\dev\\Python312\\Lib\\site-packages\\wcwidth\\table_wide.py',
   'PYMODULE'),
  ('wcwidth.table_vs16',
   'D:\\dev\\Python312\\Lib\\site-packages\\wcwidth\\table_vs16.py',
   'PYMODULE'),
  ('urwid.font',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\font.py',
   'PYMODULE'),
  ('urwid.command_map',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\command_map.py',
   'PYMODULE'),
  ('urwid.canvas',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\canvas.py',
   'PYMODULE'),
  ('urwid.text_layout',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\text_layout.py',
   'PYMODULE'),
  ('urwid.display',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\__init__.py',
   'PYMODULE'),
  ('urwid.display.curses',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\curses.py',
   'PYMODULE'),
  ('urwid.display.raw',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\raw.py',
   'PYMODULE'),
  ('urwid.display._posix_raw_display',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\_posix_raw_display.py',
   'PYMODULE'),
  ('urwid.display._win32_raw_display',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\_win32_raw_display.py',
   'PYMODULE'),
  ('urwid.display._win32',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\_win32.py',
   'PYMODULE'),
  ('urwid.display._raw_display_base',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\display\\_raw_display_base.py',
   'PYMODULE'),
  ('urwid.signals',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\signals.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.window',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\window.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.statusbar',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\statusbar.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.commander.commander',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\commander\\commander.py',
   'PYMODULE'),
  ('mitmproxy.types',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\types.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.commander',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\commander\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.quickhelp',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\quickhelp.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.grideditor.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\grideditor\\base.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.options',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\options.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.keybindings',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\keybindings.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.help',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\help.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.flowview',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\flowview.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.tabs',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\tabs.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.flowdetailview',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\flowdetailview.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.searchable',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\searchable.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.flowlist',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\flowlist.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.eventlog',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\eventlog.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.commands',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\commands.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.palettes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\palettes.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.defaultkeys',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\defaultkeys.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.consoleaddons',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\consoleaddons.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.commandexecutor',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\commandexecutor.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.overlay',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\overlay.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.keymap',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\keymap.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.grideditor',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\grideditor\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.grideditor.editors',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\grideditor\\editors.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.grideditor.col_viewany',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\grideditor\\col_viewany.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.grideditor.col_text',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\grideditor\\col_text.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.grideditor.col_subgrid',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\grideditor\\col_subgrid.py',
   'PYMODULE'),
  ('mitmproxy.net.http.cookies',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\cookies.py',
   'PYMODULE'),
  ('mitmproxy.coretypes.multidict',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\coretypes\\multidict.py',
   'PYMODULE'),
  ('mitmproxy.coretypes.serializable',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\coretypes\\serializable.py',
   'PYMODULE'),
  ('uuid', 'D:\\dev\\Python312\\Lib\\uuid.py', 'PYMODULE'),
  ('mitmproxy.coretypes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\coretypes\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.net.http',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.net.http.http1',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\http1\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.net.http.http1.read',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\http1\\read.py',
   'PYMODULE'),
  ('mitmproxy.net.http.http1.assemble',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\http1\\assemble.py',
   'PYMODULE'),
  ('mitmproxy.net.http.validate',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\validate.py',
   'PYMODULE'),
  ('mitmproxy.net.http.url',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\url.py',
   'PYMODULE'),
  ('mitmproxy.net.check',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\check.py',
   'PYMODULE'),
  ('mitmproxy.net.http.status_codes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\status_codes.py',
   'PYMODULE'),
  ('mitmproxy.net.http.multipart',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\multipart.py',
   'PYMODULE'),
  ('mitmproxy.net.http.headers',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\http\\headers.py',
   'PYMODULE'),
  ('mitmproxy.net',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.net.tls',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\tls.py',
   'PYMODULE'),
  ('mitmproxy.net.encoding',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\encoding.py',
   'PYMODULE'),
  ('mitmproxy.net.server_spec',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\server_spec.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.grideditor.col_bytes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\grideditor\\col_bytes.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.signals',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\signals.py',
   'PYMODULE'),
  ('mitmproxy.tools.console.layoutwidget',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\console\\layoutwidget.py',
   'PYMODULE'),
  ('mitmproxy.tools',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.flow',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\flow.py',
   'PYMODULE'),
  ('publicsuffix2',
   'D:\\dev\\Python312\\Lib\\site-packages\\publicsuffix2\\__init__.py',
   'PYMODULE'),
  ('urwid.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\urwid\\util.py',
   'PYMODULE'),
  ('mitmproxy.utils.sliding_window',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\sliding_window.py',
   'PYMODULE'),
  ('mitmproxy.utils.strutils',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\strutils.py',
   'PYMODULE'),
  ('mitmproxy.utils.typecheck',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\typecheck.py',
   'PYMODULE'),
  ('mitmproxy.utils.signals',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\signals.py',
   'PYMODULE'),
  ('mitmproxy.utils.vt_codes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\vt_codes.py',
   'PYMODULE'),
  ('mitmproxy.addons.dumper',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\dumper.py',
   'PYMODULE'),
  ('mitmproxy.websocket',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\websocket.py',
   'PYMODULE'),
  ('mitmproxy.net.dns.response_codes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\dns\\response_codes.py',
   'PYMODULE'),
  ('mitmproxy.net.dns',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\dns\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.net.dns.op_codes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\dns\\op_codes.py',
   'PYMODULE'),
  ('mitmproxy.net.dns.https_records',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\dns\\https_records.py',
   'PYMODULE'),
  ('mitmproxy.net.dns.domain_names',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\dns\\domain_names.py',
   'PYMODULE'),
  ('mitmproxy.net.dns.types',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\dns\\types.py',
   'PYMODULE'),
  ('mitmproxy.net.dns.classes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\dns\\classes.py',
   'PYMODULE'),
  ('mitmproxy.contrib.click',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\click\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.contrib',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.contrib.imghdr',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\imghdr.py',
   'PYMODULE'),
  ('mitmproxy_rs',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_rs\\__init__.py',
   'PYMODULE'),
  ('mitmproxy_windows',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_windows\\__init__.py',
   'PYMODULE'),
  ('wsproto.frame_protocol',
   'D:\\dev\\Python312\\Lib\\site-packages\\wsproto\\frame_protocol.py',
   'PYMODULE'),
  ('wsproto',
   'D:\\dev\\Python312\\Lib\\site-packages\\wsproto\\__init__.py',
   'PYMODULE'),
  ('wsproto.typing',
   'D:\\dev\\Python312\\Lib\\site-packages\\wsproto\\typing.py',
   'PYMODULE'),
  ('wsproto.handshake',
   'D:\\dev\\Python312\\Lib\\site-packages\\wsproto\\handshake.py',
   'PYMODULE'),
  ('wsproto.utilities',
   'D:\\dev\\Python312\\Lib\\site-packages\\wsproto\\utilities.py',
   'PYMODULE'),
  ('h11._headers',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._events',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._util',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._abnf',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._version',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._state',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._writers',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._readers',
   'D:\\dev\\Python312\\Lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('wsproto.events',
   'D:\\dev\\Python312\\Lib\\site-packages\\wsproto\\events.py',
   'PYMODULE'),
  ('wsproto.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\wsproto\\connection.py',
   'PYMODULE'),
  ('wsproto.extensions',
   'D:\\dev\\Python312\\Lib\\site-packages\\wsproto\\extensions.py',
   'PYMODULE'),
  ('mitmproxy.addons.upstream_auth',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\upstream_auth.py',
   'PYMODULE'),
  ('mitmproxy.proxy.mode_specs',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\mode_specs.py',
   'PYMODULE'),
  ('mitmproxy.proxy',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.proxy.server',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\server.py',
   'PYMODULE'),
  ('mitmproxy.utils.data',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\data.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.proxy.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\utils.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._upstream_proxy',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_upstream_proxy.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.websocket',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\websocket.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.tls',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\tls.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.udp',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\udp.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.tcp',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\tcp.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._http3',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_http3.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._http_h3',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_http_h3.py',
   'PYMODULE'),
  ('aioquic.quic.packet',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\packet.py',
   'PYMODULE'),
  ('aioquic.quic',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\__init__.py',
   'PYMODULE'),
  ('aioquic',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\__init__.py',
   'PYMODULE'),
  ('aioquic.quic.rangeset',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\rangeset.py',
   'PYMODULE'),
  ('aioquic.buffer',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\buffer.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('aioquic.quic.events',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\events.py',
   'PYMODULE'),
  ('aioquic.quic.configuration',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\configuration.py',
   'PYMODULE'),
  ('aioquic.quic.logger',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\logger.py',
   'PYMODULE'),
  ('aioquic.tls',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\tls.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.hkdf',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\hkdf.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('service_identity',
   'D:\\dev\\Python312\\Lib\\site-packages\\service_identity\\__init__.py',
   'PYMODULE'),
  ('service_identity.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\service_identity\\exceptions.py',
   'PYMODULE'),
  ('attr',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._version_info',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr._next_gen',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._make',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._funcs',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._cmp',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr.validators',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attr.filters',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.converters',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.setters',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr._config',
   'D:\\dev\\Python312\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('service_identity.pyopenssl',
   'D:\\dev\\Python312\\Lib\\site-packages\\service_identity\\pyopenssl.py',
   'PYMODULE'),
  ('service_identity.hazmat',
   'D:\\dev\\Python312\\Lib\\site-packages\\service_identity\\hazmat.py',
   'PYMODULE'),
  ('service_identity.cryptography',
   'D:\\dev\\Python312\\Lib\\site-packages\\service_identity\\cryptography.py',
   'PYMODULE'),
  ('pyasn1.type.char',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\char.py',
   'PYMODULE'),
  ('pyasn1.type.univ',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\univ.py',
   'PYMODULE'),
  ('pyasn1.type.tagmap',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\tagmap.py',
   'PYMODULE'),
  ('pyasn1.type.namedval',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\namedval.py',
   'PYMODULE'),
  ('pyasn1.type.namedtype',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\namedtype.py',
   'PYMODULE'),
  ('pyasn1.type.constraint',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\constraint.py',
   'PYMODULE'),
  ('pyasn1.type.error',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\error.py',
   'PYMODULE'),
  ('pyasn1.type.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\base.py',
   'PYMODULE'),
  ('pyasn1.compat.integer',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\compat\\integer.py',
   'PYMODULE'),
  ('pyasn1.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\compat\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.eoo',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\ber\\eoo.py',
   'PYMODULE'),
  ('pyasn1.codec.ber',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\ber\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.decoder',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\ber\\decoder.py',
   'PYMODULE'),
  ('pyasn1.type.useful',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\useful.py',
   'PYMODULE'),
  ('pyasn1.codec.streaming',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\streaming.py',
   'PYMODULE'),
  ('pyasn1.debug',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\debug.py',
   'PYMODULE'),
  ('pyasn1.codec',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\__init__.py',
   'PYMODULE'),
  ('pyasn1.type.tag',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\tag.py',
   'PYMODULE'),
  ('pyasn1.type',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\type\\__init__.py',
   'PYMODULE'),
  ('pyasn1.error',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\error.py',
   'PYMODULE'),
  ('pyasn1',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.der.decoder',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\der\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.der',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\der\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.cer.decoder',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\cer\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.cer',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\cer\\__init__.py',
   'PYMODULE'),
  ('aioquic.h3.events',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\h3\\events.py',
   'PYMODULE'),
  ('aioquic.h3',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\h3\\__init__.py',
   'PYMODULE'),
  ('aioquic.h3.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\h3\\connection.py',
   'PYMODULE'),
  ('aioquic.quic.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\connection.py',
   'PYMODULE'),
  ('aioquic.quic.stream',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\stream.py',
   'PYMODULE'),
  ('aioquic.quic.recovery',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\recovery.py',
   'PYMODULE'),
  ('aioquic.quic.congestion.reno',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\congestion\\reno.py',
   'PYMODULE'),
  ('aioquic.quic.congestion.cubic',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\congestion\\cubic.py',
   'PYMODULE'),
  ('aioquic.quic.congestion',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\congestion\\__init__.py',
   'PYMODULE'),
  ('aioquic.quic.packet_builder',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\packet_builder.py',
   'PYMODULE'),
  ('aioquic.quic.crypto',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\crypto.py',
   'PYMODULE'),
  ('aioquic.quic.congestion.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\quic\\congestion\\base.py',
   'PYMODULE'),
  ('aioquic.h3.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\h3\\exceptions.py',
   'PYMODULE'),
  ('pylsqpack',
   'D:\\dev\\Python312\\Lib\\site-packages\\pylsqpack\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._http2',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_http2.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._http_h2',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_http_h2.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._http1',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_http1.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._hooks',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_hooks.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._events',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_events.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.http._base',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\http\\_base.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.quic',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\quic\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.quic._stream_layers',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\quic\\_stream_layers.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.quic._raw_layers',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\quic\\_raw_layers.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.quic._hooks',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\quic\\_hooks.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.quic._events',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\quic\\_events.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.quic._commands',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\quic\\_commands.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.quic._client_hello_parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\quic\\_client_hello_parser.py',
   'PYMODULE'),
  ('mitmproxy.proxy.context',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\context.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.dns',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\dns.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layers.modes',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layers\\modes.py',
   'PYMODULE'),
  ('mitmproxy.proxy.layer',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\layer.py',
   'PYMODULE'),
  ('mitmproxy.tls',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tls.py',
   'PYMODULE'),
  ('mitmproxy.contrib.kaitaistruct.tls_client_hello',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\kaitaistruct\\tls_client_hello.py',
   'PYMODULE'),
  ('mitmproxy.contrib.kaitaistruct.dtls_client_hello',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\kaitaistruct\\dtls_client_hello.py',
   'PYMODULE'),
  ('mitmproxy.contrib.kaitaistruct',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\kaitaistruct\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.contrib.kaitaistruct.png',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\kaitaistruct\\png.py',
   'PYMODULE'),
  ('mitmproxy.contrib.kaitaistruct.jpeg',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\kaitaistruct\\jpeg.py',
   'PYMODULE'),
  ('mitmproxy.contrib.kaitaistruct.exif',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\kaitaistruct\\exif.py',
   'PYMODULE'),
  ('mitmproxy.contrib.kaitaistruct.ico',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\kaitaistruct\\ico.py',
   'PYMODULE'),
  ('mitmproxy.contrib.kaitaistruct.gif',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\kaitaistruct\\gif.py',
   'PYMODULE'),
  ('kaitaistruct',
   'D:\\dev\\Python312\\Lib\\site-packages\\kaitaistruct.py',
   'PYMODULE'),
  ('mitmproxy.proxy.server_hooks',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\server_hooks.py',
   'PYMODULE'),
  ('mitmproxy.proxy.tunnel',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\tunnel.py',
   'PYMODULE'),
  ('mitmproxy.proxy.events',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\events.py',
   'PYMODULE'),
  ('mitmproxy.proxy.commands',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\commands.py',
   'PYMODULE'),
  ('mitmproxy.addons.update_alt_svc',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\update_alt_svc.py',
   'PYMODULE'),
  ('mitmproxy.addons.tlsconfig',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\tlsconfig.py',
   'PYMODULE'),
  ('mitmproxy.addons.strip_dns_https_records',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\strip_dns_https_records.py',
   'PYMODULE'),
  ('mitmproxy.addons.stickycookie',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\stickycookie.py',
   'PYMODULE'),
  ('mitmproxy.addons.stickyauth',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\stickyauth.py',
   'PYMODULE'),
  ('mitmproxy.addons.serverplayback',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\serverplayback.py',
   'PYMODULE'),
  ('mitmproxy.addons.script',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\script.py',
   'PYMODULE'),
  ('mitmproxy.addonmanager',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addonmanager.py',
   'PYMODULE'),
  ('mitmproxy.addons.savehar',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\savehar.py',
   'PYMODULE'),
  ('mitmproxy.addons.save',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\save.py',
   'PYMODULE'),
  ('mitmproxy.addons.proxyserver',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\proxyserver.py',
   'PYMODULE'),
  ('mitmproxy.proxy.mode_servers',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\proxy\\mode_servers.py',
   'PYMODULE'),
  ('mitmproxy.net.free_port',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\free_port.py',
   'PYMODULE'),
  ('mitmproxy.net.local_ip',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\net\\local_ip.py',
   'PYMODULE'),
  ('mitmproxy.addons.proxyauth',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\proxyauth.py',
   'PYMODULE'),
  ('passlib.apache',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\apache.py',
   'PYMODULE'),
  ('passlib.utils.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\utils\\compat\\__init__.py',
   'PYMODULE'),
  ('passlib.utils.decor',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\utils\\decor.py',
   'PYMODULE'),
  ('passlib.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\utils\\__init__.py',
   'PYMODULE'),
  ('passlib.utils.handlers',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\utils\\handlers.py',
   'PYMODULE'),
  ('passlib.ifc',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\ifc.py',
   'PYMODULE'),
  ('crypt', 'D:\\dev\\Python312\\Lib\\crypt.py', 'PYMODULE'),
  ('passlib.utils.binary',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\utils\\binary.py',
   'PYMODULE'),
  ('timeit', 'D:\\dev\\Python312\\Lib\\timeit.py', 'PYMODULE'),
  ('stringprep', 'D:\\dev\\Python312\\Lib\\stringprep.py', 'PYMODULE'),
  ('passlib.hash',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\hash.py',
   'PYMODULE'),
  ('passlib.handlers.windows',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\windows.py',
   'PYMODULE'),
  ('passlib.handlers',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\__init__.py',
   'PYMODULE'),
  ('passlib.crypto.des',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\des.py',
   'PYMODULE'),
  ('passlib.crypto',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\__init__.py',
   'PYMODULE'),
  ('passlib.crypto.scrypt',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\scrypt\\__init__.py',
   'PYMODULE'),
  ('passlib.crypto.scrypt._builtin',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\scrypt\\_builtin.py',
   'PYMODULE'),
  ('passlib.crypto.scrypt._salsa',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\scrypt\\_salsa.py',
   'PYMODULE'),
  ('passlib.crypto.digest',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\digest.py',
   'PYMODULE'),
  ('passlib.crypto._md4',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\_md4.py',
   'PYMODULE'),
  ('passlib.handlers.sun_md5_crypt',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\sun_md5_crypt.py',
   'PYMODULE'),
  ('passlib.handlers.sha2_crypt',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\sha2_crypt.py',
   'PYMODULE'),
  ('passlib.handlers.sha1_crypt',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\sha1_crypt.py',
   'PYMODULE'),
  ('passlib.handlers.scrypt',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\scrypt.py',
   'PYMODULE'),
  ('passlib.handlers.scram',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\scram.py',
   'PYMODULE'),
  ('passlib.handlers.roundup',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\roundup.py',
   'PYMODULE'),
  ('passlib.handlers.postgres',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\postgres.py',
   'PYMODULE'),
  ('passlib.handlers.phpass',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\phpass.py',
   'PYMODULE'),
  ('passlib.handlers.pbkdf2',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\pbkdf2.py',
   'PYMODULE'),
  ('passlib.handlers.oracle',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\oracle.py',
   'PYMODULE'),
  ('passlib.handlers.mysql',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\mysql.py',
   'PYMODULE'),
  ('passlib.handlers.mssql',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\mssql.py',
   'PYMODULE'),
  ('passlib.handlers.misc',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\misc.py',
   'PYMODULE'),
  ('passlib.handlers.md5_crypt',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\md5_crypt.py',
   'PYMODULE'),
  ('passlib.handlers.ldap_digests',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\ldap_digests.py',
   'PYMODULE'),
  ('passlib.handlers.fshp',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\fshp.py',
   'PYMODULE'),
  ('passlib.handlers.django',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\django.py',
   'PYMODULE'),
  ('passlib.handlers.digests',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\digests.py',
   'PYMODULE'),
  ('passlib.handlers.des_crypt',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\des_crypt.py',
   'PYMODULE'),
  ('passlib.handlers.cisco',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\cisco.py',
   'PYMODULE'),
  ('passlib.handlers.bcrypt',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\bcrypt.py',
   'PYMODULE'),
  ('passlib.crypto._blowfish',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\_blowfish\\__init__.py',
   'PYMODULE'),
  ('passlib.crypto._blowfish.unrolled',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\_blowfish\\unrolled.py',
   'PYMODULE'),
  ('passlib.crypto._blowfish.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\crypto\\_blowfish\\base.py',
   'PYMODULE'),
  ('passlib.handlers.argon2',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\handlers\\argon2.py',
   'PYMODULE'),
  ('argon2',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2\\__init__.py',
   'PYMODULE'),
  ('argon2._utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2\\_utils.py',
   'PYMODULE'),
  ('argon2._password_hasher',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2\\_password_hasher.py',
   'PYMODULE'),
  ('argon2._typing',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2\\_typing.py',
   'PYMODULE'),
  ('argon2._legacy',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2\\_legacy.py',
   'PYMODULE'),
  ('argon2.profiles',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2\\profiles.py',
   'PYMODULE'),
  ('argon2.low_level',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2\\low_level.py',
   'PYMODULE'),
  ('_argon2_cffi_bindings',
   'D:\\dev\\Python312\\Lib\\site-packages\\_argon2_cffi_bindings\\__init__.py',
   'PYMODULE'),
  ('argon2.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2\\exceptions.py',
   'PYMODULE'),
  ('passlib.context',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\context.py',
   'PYMODULE'),
  ('passlib.registry',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\registry.py',
   'PYMODULE'),
  ('passlib.exc',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\exc.py',
   'PYMODULE'),
  ('passlib',
   'D:\\dev\\Python312\\Lib\\site-packages\\passlib\\__init__.py',
   'PYMODULE'),
  ('ldap3',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\__init__.py',
   'PYMODULE'),
  ('ldap3.protocol.rfc4512',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\rfc4512.py',
   'PYMODULE'),
  ('ldap3.protocol',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\__init__.py',
   'PYMODULE'),
  ('ldap3.core.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\exceptions.py',
   'PYMODULE'),
  ('ldap3.core',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\__init__.py',
   'PYMODULE'),
  ('ldap3.core.results',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\results.py',
   'PYMODULE'),
  ('ldap3.protocol.formatters.standard',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\formatters\\standard.py',
   'PYMODULE'),
  ('ldap3.protocol.formatters',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\formatters\\__init__.py',
   'PYMODULE'),
  ('ldap3.protocol.formatters.validators',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\formatters\\validators.py',
   'PYMODULE'),
  ('ldap3.protocol.formatters.formatters',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\formatters\\formatters.py',
   'PYMODULE'),
  ('ldap3.core.timezone',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\timezone.py',
   'PYMODULE'),
  ('ldap3.utils.ciDict',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\ciDict.py',
   'PYMODULE'),
  ('ldap3.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\__init__.py',
   'PYMODULE'),
  ('ldap3.utils.conv',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\conv.py',
   'PYMODULE'),
  ('ldap3.protocol.oid',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\oid.py',
   'PYMODULE'),
  ('ldap3.abstract.cursor',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\abstract\\cursor.py',
   'PYMODULE'),
  ('ldap3.utils.log',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\log.py',
   'PYMODULE'),
  ('ldap3.protocol.rfc4511',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\rfc4511.py',
   'PYMODULE'),
  ('ldap3.utils.dn',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\dn.py',
   'PYMODULE'),
  ('ldap3.abstract',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\abstract\\__init__.py',
   'PYMODULE'),
  ('ldap3.abstract.entry',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\abstract\\entry.py',
   'PYMODULE'),
  ('ldap3.utils.repr',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\repr.py',
   'PYMODULE'),
  ('ldap3.protocol.rfc2849',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\rfc2849.py',
   'PYMODULE'),
  ('ldap3.utils.asn1',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\asn1.py',
   'PYMODULE'),
  ('ldap3.protocol.convert',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\convert.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.encoder',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyasn1\\codec\\ber\\encoder.py',
   'PYMODULE'),
  ('ldap3.protocol.persistentSearch',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\persistentSearch.py',
   'PYMODULE'),
  ('ldap3.protocol.controls',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\controls.py',
   'PYMODULE'),
  ('ldap3.utils.ordDict',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\ordDict.py',
   'PYMODULE'),
  ('ldap3.abstract.attribute',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\abstract\\attribute.py',
   'PYMODULE'),
  ('ldap3.abstract.attrDef',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\abstract\\attrDef.py',
   'PYMODULE'),
  ('ldap3.abstract.objectDef',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\abstract\\objectDef.py',
   'PYMODULE'),
  ('ldap3.core.rdns',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\rdns.py',
   'PYMODULE'),
  ('ldap3.core.pooling',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\pooling.py',
   'PYMODULE'),
  ('ldap3.core.tls',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\tls.py',
   'PYMODULE'),
  ('ldap3.utils.tls_backport',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\tls_backport.py',
   'PYMODULE'),
  ('ldap3.core.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\connection.py',
   'PYMODULE'),
  ('ldap3.utils.ntlm',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\ntlm.py',
   'PYMODULE'),
  ('ldap3.protocol.sasl.kerberos',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\sasl\\kerberos.py',
   'PYMODULE'),
  ('ldap3.protocol.sasl',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\sasl\\__init__.py',
   'PYMODULE'),
  ('ldap3.protocol.sasl.sasl',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\sasl\\sasl.py',
   'PYMODULE'),
  ('ldap3.utils.port_validators',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\port_validators.py',
   'PYMODULE'),
  ('ldap3.core.usage',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\usage.py',
   'PYMODULE'),
  ('ldap3.protocol.rfc2696',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\rfc2696.py',
   'PYMODULE'),
  ('ldap3.operation.unbind',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\unbind.py',
   'PYMODULE'),
  ('ldap3.operation',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\__init__.py',
   'PYMODULE'),
  ('ldap3.strategy.asyncStream',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\asyncStream.py',
   'PYMODULE'),
  ('ldap3.strategy',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\__init__.py',
   'PYMODULE'),
  ('ldap3.strategy.mockSync',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\mockSync.py',
   'PYMODULE'),
  ('ldap3.strategy.mockBase',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\mockBase.py',
   'PYMODULE'),
  ('ldap3.strategy.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\base.py',
   'PYMODULE'),
  ('ldap3.protocol.microsoft',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\microsoft.py',
   'PYMODULE'),
  ('ldap3.utils.uri',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\uri.py',
   'PYMODULE'),
  ('ldap3.strategy.ldifProducer',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\ldifProducer.py',
   'PYMODULE'),
  ('ldap3.strategy.restartable',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\restartable.py',
   'PYMODULE'),
  ('ldap3.strategy.reusable',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\reusable.py',
   'PYMODULE'),
  ('ldap3.strategy.asynchronous',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\asynchronous.py',
   'PYMODULE'),
  ('ldap3.strategy.mockAsync',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\mockAsync.py',
   'PYMODULE'),
  ('ldap3.strategy.safeRestartable',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\safeRestartable.py',
   'PYMODULE'),
  ('ldap3.strategy.safeSync',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\safeSync.py',
   'PYMODULE'),
  ('ldap3.strategy.sync',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\strategy\\sync.py',
   'PYMODULE'),
  ('ldap3.protocol.sasl.plain',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\sasl\\plain.py',
   'PYMODULE'),
  ('ldap3.protocol.sasl.external',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\sasl\\external.py',
   'PYMODULE'),
  ('ldap3.protocol.sasl.digestMd5',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\sasl\\digestMd5.py',
   'PYMODULE'),
  ('ldap3.operation.search',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\search.py',
   'PYMODULE'),
  ('ldap3.operation.modifyDn',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\modifyDn.py',
   'PYMODULE'),
  ('ldap3.operation.modify',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\modify.py',
   'PYMODULE'),
  ('ldap3.operation.extended',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\extended.py',
   'PYMODULE'),
  ('ldap3.operation.delete',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\delete.py',
   'PYMODULE'),
  ('ldap3.operation.compare',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\compare.py',
   'PYMODULE'),
  ('ldap3.operation.bind',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\bind.py',
   'PYMODULE'),
  ('ldap3.operation.add',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\add.py',
   'PYMODULE'),
  ('ldap3.operation.abandon',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\operation\\abandon.py',
   'PYMODULE'),
  ('ldap3.extend',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\__init__.py',
   'PYMODULE'),
  ('ldap3.extend.standard.PersistentSearch',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\standard\\PersistentSearch.py',
   'PYMODULE'),
  ('ldap3.extend.standard',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\standard\\__init__.py',
   'PYMODULE'),
  ('ldap3.extend.standard.PagedSearch',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\standard\\PagedSearch.py',
   'PYMODULE'),
  ('ldap3.extend.standard.modifyPassword',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\standard\\modifyPassword.py',
   'PYMODULE'),
  ('ldap3.utils.hashed',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\hashed.py',
   'PYMODULE'),
  ('ldap3.protocol.rfc3062',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\rfc3062.py',
   'PYMODULE'),
  ('ldap3.extend.operation',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\operation.py',
   'PYMODULE'),
  ('ldap3.extend.standard.whoAmI',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\standard\\whoAmI.py',
   'PYMODULE'),
  ('ldap3.extend.novell.checkGroupsMemberships',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\checkGroupsMemberships.py',
   'PYMODULE'),
  ('ldap3.extend.novell',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\__init__.py',
   'PYMODULE'),
  ('ldap3.extend.novell.removeMembersFromGroups',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\removeMembersFromGroups.py',
   'PYMODULE'),
  ('ldap3.extend.novell.addMembersToGroups',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\addMembersToGroups.py',
   'PYMODULE'),
  ('ldap3.extend.novell.endTransaction',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\endTransaction.py',
   'PYMODULE'),
  ('ldap3.protocol.novell',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\novell.py',
   'PYMODULE'),
  ('ldap3.extend.novell.startTransaction',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\startTransaction.py',
   'PYMODULE'),
  ('ldap3.extend.novell.nmasSetUniversalPassword',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\nmasSetUniversalPassword.py',
   'PYMODULE'),
  ('ldap3.extend.novell.nmasGetUniversalPassword',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\nmasGetUniversalPassword.py',
   'PYMODULE'),
  ('ldap3.extend.novell.getBindDn',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\getBindDn.py',
   'PYMODULE'),
  ('ldap3.extend.novell.listReplicas',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\listReplicas.py',
   'PYMODULE'),
  ('ldap3.extend.novell.replicaInfo',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\replicaInfo.py',
   'PYMODULE'),
  ('ldap3.extend.novell.partition_entry_count',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\novell\\partition_entry_count.py',
   'PYMODULE'),
  ('ldap3.extend.microsoft.persistentSearch',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\microsoft\\persistentSearch.py',
   'PYMODULE'),
  ('ldap3.extend.microsoft',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\microsoft\\__init__.py',
   'PYMODULE'),
  ('ldap3.extend.microsoft.removeMembersFromGroups',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\microsoft\\removeMembersFromGroups.py',
   'PYMODULE'),
  ('ldap3.extend.microsoft.addMembersToGroups',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\microsoft\\addMembersToGroups.py',
   'PYMODULE'),
  ('ldap3.extend.microsoft.unlockAccount',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\microsoft\\unlockAccount.py',
   'PYMODULE'),
  ('ldap3.extend.microsoft.modifyPassword',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\microsoft\\modifyPassword.py',
   'PYMODULE'),
  ('ldap3.extend.microsoft.dirSync',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\extend\\microsoft\\dirSync.py',
   'PYMODULE'),
  ('ldap3.core.server',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\core\\server.py',
   'PYMODULE'),
  ('ldap3.protocol.schemas.ds389',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\schemas\\ds389.py',
   'PYMODULE'),
  ('ldap3.protocol.schemas',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\schemas\\__init__.py',
   'PYMODULE'),
  ('ldap3.protocol.schemas.slapd24',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\schemas\\slapd24.py',
   'PYMODULE'),
  ('ldap3.protocol.schemas.ad2012R2',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\schemas\\ad2012R2.py',
   'PYMODULE'),
  ('ldap3.protocol.schemas.edir914',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\schemas\\edir914.py',
   'PYMODULE'),
  ('ldap3.protocol.schemas.edir888',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\protocol\\schemas\\edir888.py',
   'PYMODULE'),
  ('ldap3.utils.config',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\utils\\config.py',
   'PYMODULE'),
  ('ldap3.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\ldap3\\version.py',
   'PYMODULE'),
  ('future.types.newstr',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\types\\newstr.py',
   'PYMODULE'),
  ('future.types.newbytes',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\types\\newbytes.py',
   'PYMODULE'),
  ('future.utils.surrogateescape',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\utils\\surrogateescape.py',
   'PYMODULE'),
  ('future',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\__init__.py',
   'PYMODULE'),
  ('future.standard_library',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\standard_library\\__init__.py',
   'PYMODULE'),
  ('future.moves.dbm.ndbm',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\moves\\dbm\\ndbm.py',
   'PYMODULE'),
  ('dbm.ndbm', 'D:\\dev\\Python312\\Lib\\dbm\\ndbm.py', 'PYMODULE'),
  ('future.moves.dbm.gnu',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\moves\\dbm\\gnu.py',
   'PYMODULE'),
  ('dbm.gnu', 'D:\\dev\\Python312\\Lib\\dbm\\gnu.py', 'PYMODULE'),
  ('future.moves.dbm.dumb',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\moves\\dbm\\dumb.py',
   'PYMODULE'),
  ('dbm.dumb', 'D:\\dev\\Python312\\Lib\\dbm\\dumb.py', 'PYMODULE'),
  ('future.moves.dbm',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\moves\\dbm\\__init__.py',
   'PYMODULE'),
  ('future.moves',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\moves\\__init__.py',
   'PYMODULE'),
  ('dbm', 'D:\\dev\\Python312\\Lib\\dbm\\__init__.py', 'PYMODULE'),
  ('future.moves.test.support',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\moves\\test\\support.py',
   'PYMODULE'),
  ('test.test_support',
   'D:\\dev\\Python312\\Lib\\test\\test_support.py',
   'PYMODULE'),
  ('sched', 'D:\\dev\\Python312\\Lib\\sched.py', 'PYMODULE'),
  ('test.support.socket_helper',
   'D:\\dev\\Python312\\Lib\\test\\support\\socket_helper.py',
   'PYMODULE'),
  ('test.support.script_helper',
   'D:\\dev\\Python312\\Lib\\test\\support\\script_helper.py',
   'PYMODULE'),
  ('test.support.import_helper',
   'D:\\dev\\Python312\\Lib\\test\\support\\import_helper.py',
   'PYMODULE'),
  ('test.support.warnings_helper',
   'D:\\dev\\Python312\\Lib\\test\\support\\warnings_helper.py',
   'PYMODULE'),
  ('test.support.os_helper',
   'D:\\dev\\Python312\\Lib\\test\\support\\os_helper.py',
   'PYMODULE'),
  ('test.support',
   'D:\\dev\\Python312\\Lib\\test\\support\\__init__.py',
   'PYMODULE'),
  ('asyncio.events', 'D:\\dev\\Python312\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\dev\\Python312\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\dev\\Python312\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('tracemalloc', 'D:\\dev\\Python312\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tkinter', 'D:\\dev\\Python312\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.constants',
   'D:\\dev\\Python312\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('future.moves.test',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\moves\\test\\__init__.py',
   'PYMODULE'),
  ('test', 'D:\\dev\\Python312\\Lib\\test\\__init__.py', 'PYMODULE'),
  ('future.backports.urllib.robotparser',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\urllib\\robotparser.py',
   'PYMODULE'),
  ('future.backports',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\__init__.py',
   'PYMODULE'),
  ('future.backports.misc',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\misc.py',
   'PYMODULE'),
  ('future.backports.email',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\__init__.py',
   'PYMODULE'),
  ('future.backports.email.parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\parser.py',
   'PYMODULE'),
  ('future.backports.email._policybase',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\_policybase.py',
   'PYMODULE'),
  ('future.backports.email.charset',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\charset.py',
   'PYMODULE'),
  ('future.backports.email.encoders',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\encoders.py',
   'PYMODULE'),
  ('future.backports.email.feedparser',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\feedparser.py',
   'PYMODULE'),
  ('future.backports.email.message',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\message.py',
   'PYMODULE'),
  ('future.backports.email.iterators',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\iterators.py',
   'PYMODULE'),
  ('future.backports.email.generator',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\generator.py',
   'PYMODULE'),
  ('future.backports.email._encoded_words',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\_encoded_words.py',
   'PYMODULE'),
  ('uu', 'D:\\dev\\Python312\\Lib\\uu.py', 'PYMODULE'),
  ('optparse', 'D:\\dev\\Python312\\Lib\\optparse.py', 'PYMODULE'),
  ('future.backports.email.header',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\header.py',
   'PYMODULE'),
  ('future.backports.email.quoprimime',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\quoprimime.py',
   'PYMODULE'),
  ('future.backports.email.base64mime',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\base64mime.py',
   'PYMODULE'),
  ('future.backports.email.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\utils.py',
   'PYMODULE'),
  ('future.backports.email._parseaddr',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\_parseaddr.py',
   'PYMODULE'),
  ('future.backports.email.errors',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\email\\errors.py',
   'PYMODULE'),
  ('future.backports.datetime',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\datetime.py',
   'PYMODULE'),
  ('future.builtins',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\builtins\\__init__.py',
   'PYMODULE'),
  ('future.types.newrange',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\types\\newrange.py',
   'PYMODULE'),
  ('future.types.newlist',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\types\\newlist.py',
   'PYMODULE'),
  ('future.types.newint',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\types\\newint.py',
   'PYMODULE'),
  ('future.types.newdict',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\types\\newdict.py',
   'PYMODULE'),
  ('future.builtins.misc',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\builtins\\misc.py',
   'PYMODULE'),
  ('future.builtins.new_min_max',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\builtins\\new_min_max.py',
   'PYMODULE'),
  ('future.builtins.newsuper',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\builtins\\newsuper.py',
   'PYMODULE'),
  ('future.builtins.newround',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\builtins\\newround.py',
   'PYMODULE'),
  ('future.builtins.newnext',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\builtins\\newnext.py',
   'PYMODULE'),
  ('future.builtins.iterators',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\builtins\\iterators.py',
   'PYMODULE'),
  ('future.backports.urllib.error',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\urllib\\error.py',
   'PYMODULE'),
  ('future.backports.urllib.parse',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\urllib\\parse.py',
   'PYMODULE'),
  ('future.backports.urllib.response',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\urllib\\response.py',
   'PYMODULE'),
  ('future.backports.urllib.request',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\urllib\\request.py',
   'PYMODULE'),
  ('future.backports.http.cookiejar',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\http\\cookiejar.py',
   'PYMODULE'),
  ('future.backports.http.client',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\http\\client.py',
   'PYMODULE'),
  ('future.backports.http',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\http\\__init__.py',
   'PYMODULE'),
  ('future.backports.urllib',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\backports\\urllib\\__init__.py',
   'PYMODULE'),
  ('future.types.newobject',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\types\\newobject.py',
   'PYMODULE'),
  ('future.types',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\types\\__init__.py',
   'PYMODULE'),
  ('future.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\future\\utils\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.addons.onboarding',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\onboarding.py',
   'PYMODULE'),
  ('mitmproxy.addons.onboardingapp',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\onboardingapp\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.utils.magisk',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\magisk.py',
   'PYMODULE'),
  ('flask',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.wrappers',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.sansio.scaffold',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\sansio\\scaffold.py',
   'PYMODULE'),
  ('click',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click.parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('click._compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._winconsole',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.termui',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click.globals',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.formatting',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.decorators',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.core',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.types',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('flask.sansio', '-', 'PYMODULE'),
  ('werkzeug.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.test',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.http',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('werkzeug.security',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('markupsafe',
   'D:\\dev\\Python312\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\dev\\Python312\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('jinja2',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2.ext',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.debug',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('flask.sansio.app',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\sansio\\app.py',
   'PYMODULE'),
  ('flask.sansio.blueprints',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\sansio\\blueprints.py',
   'PYMODULE'),
  ('flask.testing',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.sessions',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.json.tag',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('itsdangerous',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('click.testing',
   'D:\\dev\\Python312\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('flask.logging',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('werkzeug.local',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('flask.json.provider',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'D:\\dev\\Python312\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('flask.templating',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.signals',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('blinker',
   'D:\\dev\\Python312\\Lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('blinker._utilities',
   'D:\\dev\\Python312\\Lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('flask.helpers',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.globals',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.ctx',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.config',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.blueprints',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.app',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('asgiref.sync',
   'D:\\dev\\Python312\\Lib\\site-packages\\asgiref\\sync.py',
   'PYMODULE'),
  ('asgiref',
   'D:\\dev\\Python312\\Lib\\site-packages\\asgiref\\__init__.py',
   'PYMODULE'),
  ('asgiref.local',
   'D:\\dev\\Python312\\Lib\\site-packages\\asgiref\\local.py',
   'PYMODULE'),
  ('asgiref.current_thread_executor',
   'D:\\dev\\Python312\\Lib\\site-packages\\asgiref\\current_thread_executor.py',
   'PYMODULE'),
  ('flask.cli',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('dotenv',
   'D:\\dev\\Python312\\Lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'D:\\dev\\Python312\\Lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'D:\\dev\\Python312\\Lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.variables',
   'D:\\dev\\Python312\\Lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('dotenv.parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('flask.typing',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.json',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.addons.asgiapp',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\asgiapp.py',
   'PYMODULE'),
  ('asgiref.wsgi',
   'D:\\dev\\Python312\\Lib\\site-packages\\asgiref\\wsgi.py',
   'PYMODULE'),
  ('asgiref.compatibility',
   'D:\\dev\\Python312\\Lib\\site-packages\\asgiref\\compatibility.py',
   'PYMODULE'),
  ('mitmproxy.addons.next_layer',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\next_layer.py',
   'PYMODULE'),
  ('mitmproxy.addons.modifyheaders',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\modifyheaders.py',
   'PYMODULE'),
  ('mitmproxy.utils.spec',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\utils\\spec.py',
   'PYMODULE'),
  ('mitmproxy.addons.modifybody',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\modifybody.py',
   'PYMODULE'),
  ('mitmproxy.addons.mapremote',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\mapremote.py',
   'PYMODULE'),
  ('mitmproxy.addons.maplocal',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\maplocal.py',
   'PYMODULE'),
  ('mitmproxy.addons.export',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\export.py',
   'PYMODULE'),
  ('pyperclip',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.addons.dns_resolver',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\dns_resolver.py',
   'PYMODULE'),
  ('mitmproxy.addons.disable_h2c',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\disable_h2c.py',
   'PYMODULE'),
  ('mitmproxy.addons.cut',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\cut.py',
   'PYMODULE'),
  ('mitmproxy.addons.core',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\core.py',
   'PYMODULE'),
  ('mitmproxy.addons.comment',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\comment.py',
   'PYMODULE'),
  ('mitmproxy.addons.command_history',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\command_history.py',
   'PYMODULE'),
  ('mitmproxy.addons.clientplayback',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\clientplayback.py',
   'PYMODULE'),
  ('mitmproxy.addons.browser',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\browser.py',
   'PYMODULE'),
  ('mitmproxy.addons.blocklist',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\blocklist.py',
   'PYMODULE'),
  ('mitmproxy.addons.block',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\block.py',
   'PYMODULE'),
  ('mitmproxy.addons.anticomp',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\anticomp.py',
   'PYMODULE'),
  ('mitmproxy.addons.anticache',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\anticache.py',
   'PYMODULE'),
  ('mitmproxy.addons.view',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\view.py',
   'PYMODULE'),
  ('sortedcontainers',
   'D:\\dev\\Python312\\Lib\\site-packages\\sortedcontainers\\__init__.py',
   'PYMODULE'),
  ('sortedcontainers.sorteddict',
   'D:\\dev\\Python312\\Lib\\site-packages\\sortedcontainers\\sorteddict.py',
   'PYMODULE'),
  ('sortedcontainers.sortedset',
   'D:\\dev\\Python312\\Lib\\site-packages\\sortedcontainers\\sortedset.py',
   'PYMODULE'),
  ('sortedcontainers.sortedlist',
   'D:\\dev\\Python312\\Lib\\site-packages\\sortedcontainers\\sortedlist.py',
   'PYMODULE'),
  ('mitmproxy.addons.readfile',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\readfile.py',
   'PYMODULE'),
  ('mitmproxy.addons.intercept',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\intercept.py',
   'PYMODULE'),
  ('mitmproxy.addons.eventstore',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\eventstore.py',
   'PYMODULE'),
  ('mitmproxy.addons.errorcheck',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\errorcheck.py',
   'PYMODULE'),
  ('mitmproxy.addons.termlog',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\termlog.py',
   'PYMODULE'),
  ('mitmproxy.platform',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\platform\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.platform.windows',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\platform\\windows.py',
   'PYMODULE'),
  ('pydivert.consts',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\consts.py',
   'PYMODULE'),
  ('pydivert',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\__init__.py',
   'PYMODULE'),
  ('pydivert.windivert',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\windivert.py',
   'PYMODULE'),
  ('pydivert.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\util.py',
   'PYMODULE'),
  ('pydivert.packet',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\packet\\__init__.py',
   'PYMODULE'),
  ('pydivert.packet.udp',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\packet\\udp.py',
   'PYMODULE'),
  ('pydivert.packet.tcp',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\packet\\tcp.py',
   'PYMODULE'),
  ('pydivert.packet.ip',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\packet\\ip.py',
   'PYMODULE'),
  ('pydivert.packet.icmp',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\packet\\icmp.py',
   'PYMODULE'),
  ('pydivert.packet.header',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\packet\\header.py',
   'PYMODULE'),
  ('pydivert.windivert_dll',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\windivert_dll\\__init__.py',
   'PYMODULE'),
  ('pydivert.windivert_dll.structs',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\windivert_dll\\structs.py',
   'PYMODULE'),
  ('mitmproxy.platform.openbsd',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\platform\\openbsd.py',
   'PYMODULE'),
  ('mitmproxy.platform.osx',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\platform\\osx.py',
   'PYMODULE'),
  ('mitmproxy.platform.pf',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\platform\\pf.py',
   'PYMODULE'),
  ('mitmproxy.platform.linux',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\platform\\linux.py',
   'PYMODULE'),
  ('mitmproxy.eventsequence',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\eventsequence.py',
   'PYMODULE'),
  ('mitmproxy.contentviews',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.contentviews.base',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\base.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_xml_html',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_xml_html.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_wbxml',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_wbxml.py',
   'PYMODULE'),
  ('mitmproxy.contrib.wbxml.ASCommandResponse',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\wbxml\\ASCommandResponse.py',
   'PYMODULE'),
  ('mitmproxy.contrib.wbxml.ASWBXML',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\wbxml\\ASWBXML.py',
   'PYMODULE'),
  ('mitmproxy.contrib.wbxml.InvalidDataException',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\wbxml\\InvalidDataException.py',
   'PYMODULE'),
  ('mitmproxy.contrib.wbxml.GlobalTokens',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\wbxml\\GlobalTokens.py',
   'PYMODULE'),
  ('mitmproxy.contrib.wbxml.ASWBXMLByteQueue',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\wbxml\\ASWBXMLByteQueue.py',
   'PYMODULE'),
  ('mitmproxy.contrib.wbxml.ASWBXMLCodePage',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\wbxml\\ASWBXMLCodePage.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\dev\\Python312\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\dev\\Python312\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\dev\\Python312\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\dev\\Python312\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\dev\\Python312\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\dev\\Python312\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\dev\\Python312\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom', 'D:\\dev\\Python312\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('mitmproxy.contrib.wbxml',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contrib\\wbxml\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_urlencoded',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_urlencoded.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_socketio',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_socketio.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_raw',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_raw.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_query',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_query.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_multipart',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_multipart.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_mqtt',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_mqtt.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_json',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_json.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_javascript',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_javascript.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_image',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_image\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_image.view',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_image\\view.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_image.image_parser',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_image\\image_parser.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_http3',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_http3.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_graphql',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_graphql.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_dns',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_dns.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._view_css',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_view_css.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._utils',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_utils.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._registry',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_registry.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_compat.py',
   'PYMODULE'),
  ('mitmproxy.contentviews._api',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\contentviews\\_api.py',
   'PYMODULE'),
  ('mitmproxy.command_lexer',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\command_lexer.py',
   'PYMODULE'),
  ('pyparsing',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.common',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.core',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.util',
   'D:\\dev\\Python312\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('mitmproxy.io',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\io\\__init__.py',
   'PYMODULE'),
  ('mitmproxy.io.io',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\io\\io.py',
   'PYMODULE'),
  ('mitmproxy.io.har',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\io\\har.py',
   'PYMODULE'),
  ('mitmproxy.io.tnetstring',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\io\\tnetstring.py',
   'PYMODULE'),
  ('mitmproxy.io.compat',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\io\\compat.py',
   'PYMODULE'),
  ('mitmproxy.flowfilter',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\flowfilter.py',
   'PYMODULE'),
  ('mitmproxy.udp',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\udp.py',
   'PYMODULE'),
  ('mitmproxy.tcp',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tcp.py',
   'PYMODULE'),
  ('mitmproxy.dns',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\dns.py',
   'PYMODULE'),
  ('mitmproxy.command',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\command.py',
   'PYMODULE'),
  ('mitmproxy.optmanager',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\optmanager.py',
   'PYMODULE'),
  ('mitmproxy.hooks',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\hooks.py',
   'PYMODULE'),
  ('mitmproxy.version',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\version.py',
   'PYMODULE'),
  ('mitmproxy.exceptions',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\exceptions.py',
   'PYMODULE'),
  ('mitmproxy.connection',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\connection.py',
   'PYMODULE'),
  ('mitmproxy.certs',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\certs.py',
   'PYMODULE'),
  ('mitmproxy.master',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\master.py',
   'PYMODULE'),
  ('mitmproxy.log',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\log.py',
   'PYMODULE'),
  ('mitmproxy.http',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\http.py',
   'PYMODULE'),
  ('mitmproxy.options',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\options.py',
   'PYMODULE'),
  ('mitmproxy.tools.dump',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\tools\\dump.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\dev\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('addons', 'D:\\alone\\2.0\\MajsoulMax-main\\addons.py', 'PYMODULE'),
  ('pathlib', 'D:\\dev\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('asyncio', 'D:\\dev\\Python312\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\dev\\Python312\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'D:\\dev\\Python312\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\dev\\Python312\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\dev\\Python312\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\dev\\Python312\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\dev\\Python312\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\dev\\Python312\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\dev\\Python312\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\dev\\Python312\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\dev\\Python312\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\dev\\Python312\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues', 'D:\\dev\\Python312\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners',
   'D:\\dev\\Python312\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\dev\\Python312\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock', 'D:\\dev\\Python312\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered',
   'D:\\dev\\Python312\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\dev\\Python312\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks', 'D:\\dev\\Python312\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\dev\\Python312\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks', 'D:\\dev\\Python312\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\dev\\Python312\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\dev\\Python312\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\dev\\Python312\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\dev\\Python312\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\dev\\Python312\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\dev\\Python312\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\dev\\Python312\\Lib\\asyncio\\exceptions.py',
   'PYMODULE')],
 [('mitmproxy_windows\\WinDivert64.sys',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_windows\\WinDivert64.sys',
   'BINARY'),
  ('mitmproxy_windows\\WinDivert.dll',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_windows\\WinDivert.dll',
   'BINARY'),
  ('mitmproxy_windows\\windows-redirector.exe',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_windows\\windows-redirector.exe',
   'BINARY'),
  ('pydivert\\windivert_dll\\WinDivert32.sys',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\windivert_dll\\WinDivert32.sys',
   'BINARY'),
  ('pydivert\\windivert_dll\\WinDivert64.sys',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\windivert_dll\\WinDivert64.sys',
   'BINARY'),
  ('pydivert\\windivert_dll\\WinDivert64.dll',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\windivert_dll\\WinDivert64.dll',
   'BINARY'),
  ('pydivert\\windivert_dll\\WinDivert32.dll',
   'D:\\dev\\Python312\\Lib\\site-packages\\pydivert\\windivert_dll\\WinDivert32.dll',
   'BINARY'),
  ('python312.dll', 'D:\\dev\\Python312\\python312.dll', 'BINARY'),
  ('_lzma.pyd', 'D:\\dev\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\dev\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\dev\\Python312\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\dev\\Python312\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\dev\\Python312\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\dev\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\dev\\Python312\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\dev\\Python312\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'D:\\dev\\Python312\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\dev\\Python312\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\dev\\Python312\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\dev\\Python312\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\dev\\Python312\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ruamel_yaml.cp312-win_amd64.pyd',
   'D:\\dev\\Python312\\Lib\\site-packages\\_ruamel_yaml.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\_cffi.cp312-win_amd64.pyd',
   'D:\\dev\\Python312\\Lib\\site-packages\\zstandard\\_cffi.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\backend_c.cp312-win_amd64.pyd',
   'D:\\dev\\Python312\\Lib\\site-packages\\zstandard\\backend_c.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_brotli.cp312-win_amd64.pyd',
   'D:\\dev\\Python312\\Lib\\site-packages\\_brotli.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win_amd64.pyd',
   'D:\\dev\\Python312\\Lib\\site-packages\\_cffi_backend.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'D:\\dev\\Python312\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('tornado\\speedups.pyd',
   'D:\\dev\\Python312\\Lib\\site-packages\\tornado\\speedups.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\dev\\Python312\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('mitmproxy_rs\\mitmproxy_rs.pyd',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_rs\\mitmproxy_rs.pyd',
   'EXTENSION'),
  ('aioquic\\_buffer.pyd',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\_buffer.pyd',
   'EXTENSION'),
  ('aioquic\\_crypto.pyd',
   'D:\\dev\\Python312\\Lib\\site-packages\\aioquic\\_crypto.pyd',
   'EXTENSION'),
  ('pylsqpack\\_binding.pyd',
   'D:\\dev\\Python312\\Lib\\site-packages\\pylsqpack\\_binding.pyd',
   'EXTENSION'),
  ('_argon2_cffi_bindings\\_ffi.pyd',
   'D:\\dev\\Python312\\Lib\\site-packages\\_argon2_cffi_bindings\\_ffi.pyd',
   'EXTENSION'),
  ('_asyncio.pyd', 'D:\\dev\\Python312\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_testcapi.pyd', 'D:\\dev\\Python312\\DLLs\\_testcapi.pyd', 'EXTENSION'),
  ('_testinternalcapi.pyd',
   'D:\\dev\\Python312\\DLLs\\_testinternalcapi.pyd',
   'EXTENSION'),
  ('_tkinter.pyd', 'D:\\dev\\Python312\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp312-win_amd64.pyd',
   'D:\\dev\\Python312\\Lib\\site-packages\\markupsafe\\_speedups.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd', 'D:\\dev\\Python312\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'D:\\dev\\Python312\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'D:\\dev\\Python312\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\dev\\Python312\\VCRUNTIME140_1.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\dev\\Python312\\DLLs\\libffi-8.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libssl-3.dll', 'D:\\dev\\Python312\\DLLs\\libssl-3.dll', 'BINARY'),
  ('python3.dll', 'D:\\dev\\Python312\\python3.dll', 'BINARY'),
  ('tcl86t.dll', 'D:\\dev\\Python312\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'D:\\dev\\Python312\\DLLs\\tk86t.dll', 'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('zlib1.dll', 'D:\\dev\\Python312\\DLLs\\zlib1.dll', 'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('config\\.gitkeep',
   'D:\\alone\\2.0\\MajsoulMax-main\\config\\.gitkeep',
   'DATA'),
  ('config\\settings.mod.yaml',
   'D:\\alone\\2.0\\MajsoulMax-main\\config\\settings.mod.yaml',
   'DATA'),
  ('config\\settings.yaml',
   'D:\\alone\\2.0\\MajsoulMax-main\\config\\settings.yaml',
   'DATA'),
  ('plugin\\helper.py',
   'D:\\alone\\2.0\\MajsoulMax-main\\plugin\\helper.py',
   'DATA'),
  ('plugin\\mod.py', 'D:\\alone\\2.0\\MajsoulMax-main\\plugin\\mod.py', 'DATA'),
  ('plugin\\update_liqi.py',
   'D:\\alone\\2.0\\MajsoulMax-main\\plugin\\update_liqi.py',
   'DATA'),
  ('proto\\basic.proto',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\basic.proto',
   'DATA'),
  ('proto\\basic_pb2.py',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\basic_pb2.py',
   'DATA'),
  ('proto\\config_pb2.py',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\config_pb2.py',
   'DATA'),
  ('proto\\liqi.json',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\liqi.json',
   'DATA'),
  ('proto\\liqi.proto',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\liqi.proto',
   'DATA'),
  ('proto\\liqi_pb2.py',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\liqi_pb2.py',
   'DATA'),
  ('proto\\lqc.lqbin',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\lqc.lqbin',
   'DATA'),
  ('proto\\sheets_pb2.py',
   'D:\\alone\\2.0\\MajsoulMax-main\\proto\\sheets_pb2.py',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\dev\\Python312\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\dev\\Python312\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('cryptography-44.0.3.dist-info\\licenses\\LICENSE',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography-44.0.3.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-44.0.3.dist-info\\INSTALLER',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography-44.0.3.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-44.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography-44.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-44.0.3.dist-info\\WHEEL',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography-44.0.3.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-44.0.3.dist-info\\RECORD',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography-44.0.3.dist-info\\RECORD',
   'DATA'),
  ('cryptography-44.0.3.dist-info\\licenses\\LICENSE.BSD',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography-44.0.3.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-44.0.3.dist-info\\METADATA',
   'D:\\dev\\Python312\\Lib\\site-packages\\cryptography-44.0.3.dist-info\\METADATA',
   'DATA'),
  ('publicsuffix2\\mpl-2.0.LICENSE',
   'D:\\dev\\Python312\\Lib\\site-packages\\publicsuffix2\\mpl-2.0.LICENSE',
   'DATA'),
  ('publicsuffix2\\public_suffix_list.ABOUT',
   'D:\\dev\\Python312\\Lib\\site-packages\\publicsuffix2\\public_suffix_list.ABOUT',
   'DATA'),
  ('publicsuffix2\\public_suffix_list.dat',
   'D:\\dev\\Python312\\Lib\\site-packages\\publicsuffix2\\public_suffix_list.dat',
   'DATA'),
  ('mitmproxy_rs\\dns.pyi',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_rs\\dns.pyi',
   'DATA'),
  ('mitmproxy_rs\\py.typed',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_rs\\py.typed',
   'DATA'),
  ('mitmproxy_rs\\__init__.pyi',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_rs\\__init__.pyi',
   'DATA'),
  ('mitmproxy_rs\\certs.pyi',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_rs\\certs.pyi',
   'DATA'),
  ('mitmproxy_rs\\udp.pyi',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_rs\\udp.pyi',
   'DATA'),
  ('mitmproxy_rs\\wireguard.pyi',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_rs\\wireguard.pyi',
   'DATA'),
  ('mitmproxy_rs\\tun.pyi',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_rs\\tun.pyi',
   'DATA'),
  ('mitmproxy_rs\\process_info.pyi',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_rs\\process_info.pyi',
   'DATA'),
  ('mitmproxy_rs\\syntax_highlight.pyi',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_rs\\syntax_highlight.pyi',
   'DATA'),
  ('mitmproxy_rs\\contentviews.pyi',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_rs\\contentviews.pyi',
   'DATA'),
  ('mitmproxy_rs\\local.pyi',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_rs\\local.pyi',
   'DATA'),
  ('mitmproxy_windows\\LICENSE',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_windows\\LICENSE',
   'DATA'),
  ('mitmproxy_windows\\WinDivert.lib',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_windows\\WinDivert.lib',
   'DATA'),
  ('mitmproxy_windows\\WINDIVERT_VERSION',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy_windows\\WINDIVERT_VERSION',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\dev\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('_tcl_data\\word.tcl', 'D:\\dev\\Python312\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tk_data\\icons.tcl', 'D:\\dev\\Python312\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\init.tcl', 'D:\\dev\\Python312\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-ru.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\koi8-ru.enc',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tk_data\\button.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tk_data\\console.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tk_data\\focus.tcl', 'D:\\dev\\Python312\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tk_data\\menu.tcl', 'D:\\dev\\Python312\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-t.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\koi8-t.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:\\dev\\Python312\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.8.tm',
   'D:\\dev\\Python312\\tcl\\tcl8\\8.5\\tcltest-2.5.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tk_data\\entry.tcl', 'D:\\dev\\Python312\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'D:\\dev\\Python312\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\scale.tcl', 'D:\\dev\\Python312\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tk_data\\images\\README',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tk_data\\tk.tcl', 'D:\\dev\\Python312\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tk_data\\tclIndex', 'D:\\dev\\Python312\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('_tk_data\\comdlg.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\safe.tcl', 'D:\\dev\\Python312\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tk_data\\license.terms',
   'D:\\dev\\Python312\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tk_data\\text.tcl', 'D:\\dev\\Python312\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'D:\\dev\\Python312\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tclIndex', 'D:\\dev\\Python312\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\auto.tcl', 'D:\\dev\\Python312\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:\\dev\\Python312\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tm.tcl', 'D:\\dev\\Python312\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('mitmproxy\\addons\\onboardingapp\\templates\\icons\\certificate-solid.svg',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\onboardingapp\\templates\\icons\\certificate-solid.svg',
   'DATA'),
  ('mitmproxy\\addons\\onboardingapp\\static\\images\\mitmproxy-long.png',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\onboardingapp\\static\\images\\mitmproxy-long.png',
   'DATA'),
  ('mitmproxy\\addons\\onboardingapp\\templates\\icons\\windows-brands.svg',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\onboardingapp\\templates\\icons\\windows-brands.svg',
   'DATA'),
  ('mitmproxy\\addons\\onboardingapp\\templates\\icons\\apple-brands.svg',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\onboardingapp\\templates\\icons\\apple-brands.svg',
   'DATA'),
  ('mitmproxy\\addons\\onboardingapp\\templates\\layout.html',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\onboardingapp\\templates\\layout.html',
   'DATA'),
  ('mitmproxy\\addons\\onboardingapp\\templates\\icons\\android-brands.svg',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\onboardingapp\\templates\\icons\\android-brands.svg',
   'DATA'),
  ('mitmproxy\\addons\\onboardingapp\\templates\\index.html',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\onboardingapp\\templates\\index.html',
   'DATA'),
  ('mitmproxy\\addons\\onboardingapp\\static\\mitmproxy.css',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\onboardingapp\\static\\mitmproxy.css',
   'DATA'),
  ('mitmproxy\\addons\\onboardingapp\\templates\\icons\\linux-brands.svg',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\onboardingapp\\templates\\icons\\linux-brands.svg',
   'DATA'),
  ('mitmproxy\\addons\\onboardingapp\\static\\bootstrap.min.css',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\onboardingapp\\static\\bootstrap.min.css',
   'DATA'),
  ('mitmproxy\\addons\\onboardingapp\\templates\\icons\\firefox-browser-brands.svg',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\onboardingapp\\templates\\icons\\firefox-browser-brands.svg',
   'DATA'),
  ('mitmproxy\\addons\\onboardingapp\\static\\images\\favicon.ico',
   'D:\\dev\\Python312\\Lib\\site-packages\\mitmproxy\\addons\\onboardingapp\\static\\images\\favicon.ico',
   'DATA'),
  ('h2-4.1.0.dist-info\\METADATA',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2-4.1.0.dist-info\\METADATA',
   'DATA'),
  ('service_identity-24.2.0.dist-info\\METADATA',
   'D:\\dev\\Python312\\Lib\\site-packages\\service_identity-24.2.0.dist-info\\METADATA',
   'DATA'),
  ('h2-4.1.0.dist-info\\top_level.txt',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2-4.1.0.dist-info\\top_level.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'D:\\dev\\Python312\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.0.dist-info\\LICENSE.txt',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask-3.1.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('service_identity-24.2.0.dist-info\\RECORD',
   'D:\\dev\\Python312\\Lib\\site-packages\\service_identity-24.2.0.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'D:\\dev\\Python312\\Lib\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'D:\\dev\\Python312\\Lib\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'D:\\dev\\Python312\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'D:\\dev\\Python312\\Lib\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('argon2_cffi-23.1.0.dist-info\\RECORD',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2_cffi-23.1.0.dist-info\\RECORD',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'D:\\dev\\Python312\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'D:\\dev\\Python312\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'D:\\dev\\Python312\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'D:\\dev\\Python312\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('service_identity-24.2.0.dist-info\\WHEEL',
   'D:\\dev\\Python312\\Lib\\site-packages\\service_identity-24.2.0.dist-info\\WHEEL',
   'DATA'),
  ('service_identity-24.2.0.dist-info\\INSTALLER',
   'D:\\dev\\Python312\\Lib\\site-packages\\service_identity-24.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'D:\\dev\\Python312\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('h2-4.1.0.dist-info\\WHEEL',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2-4.1.0.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'D:\\dev\\Python312\\Lib\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('argon2_cffi-23.1.0.dist-info\\WHEEL',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2_cffi-23.1.0.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('flask-3.1.0.dist-info\\INSTALLER',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask-3.1.0.dist-info\\INSTALLER',
   'DATA'),
  ('argon2_cffi-23.1.0.dist-info\\licenses\\LICENSE',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2_cffi-23.1.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('flask-3.1.0.dist-info\\RECORD',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask-3.1.0.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-3.1.0.dist-info\\entry_points.txt',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask-3.1.0.dist-info\\entry_points.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('argon2_cffi-23.1.0.dist-info\\METADATA',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2_cffi-23.1.0.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'D:\\dev\\Python312\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('flask-3.1.0.dist-info\\METADATA',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask-3.1.0.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'D:\\dev\\Python312\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('service_identity-24.2.0.dist-info\\licenses\\LICENSE',
   'D:\\dev\\Python312\\Lib\\site-packages\\service_identity-24.2.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'D:\\dev\\Python312\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('argon2_cffi-23.1.0.dist-info\\INSTALLER',
   'D:\\dev\\Python312\\Lib\\site-packages\\argon2_cffi-23.1.0.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'D:\\dev\\Python312\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('h2-4.1.0.dist-info\\RECORD',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2-4.1.0.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'D:\\dev\\Python312\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('h2-4.1.0.dist-info\\LICENSE',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2-4.1.0.dist-info\\LICENSE',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'D:\\dev\\Python312\\Lib\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'D:\\dev\\Python312\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('h2-4.1.0.dist-info\\INSTALLER',
   'D:\\dev\\Python312\\Lib\\site-packages\\h2-4.1.0.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.0.dist-info\\WHEEL',
   'D:\\dev\\Python312\\Lib\\site-packages\\flask-3.1.0.dist-info\\WHEEL',
   'DATA'),
  ('base_library.zip',
   'D:\\alone\\2.0\\MajsoulMax-main\\build\\MajsoulMax\\base_library.zip',
   'DATA')],
 [('posixpath', 'D:\\dev\\Python312\\Lib\\posixpath.py', 'PYMODULE'),
  ('_collections_abc',
   'D:\\dev\\Python312\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('traceback', 'D:\\dev\\Python312\\Lib\\traceback.py', 'PYMODULE'),
  ('copyreg', 'D:\\dev\\Python312\\Lib\\copyreg.py', 'PYMODULE'),
  ('functools', 'D:\\dev\\Python312\\Lib\\functools.py', 'PYMODULE'),
  ('linecache', 'D:\\dev\\Python312\\Lib\\linecache.py', 'PYMODULE'),
  ('sre_constants', 'D:\\dev\\Python312\\Lib\\sre_constants.py', 'PYMODULE'),
  ('weakref', 'D:\\dev\\Python312\\Lib\\weakref.py', 'PYMODULE'),
  ('io', 'D:\\dev\\Python312\\Lib\\io.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\dev\\Python312\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\dev\\Python312\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\dev\\Python312\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\dev\\Python312\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\dev\\Python312\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\dev\\Python312\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\dev\\Python312\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\dev\\Python312\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\dev\\Python312\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\dev\\Python312\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\dev\\Python312\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\dev\\Python312\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\dev\\Python312\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\dev\\Python312\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\dev\\Python312\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\dev\\Python312\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\dev\\Python312\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\dev\\Python312\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\dev\\Python312\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\dev\\Python312\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\dev\\Python312\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\dev\\Python312\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\dev\\Python312\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem', 'D:\\dev\\Python312\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'D:\\dev\\Python312\\Lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\dev\\Python312\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\dev\\Python312\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\dev\\Python312\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\dev\\Python312\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\dev\\Python312\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\dev\\Python312\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\dev\\Python312\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\dev\\Python312\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\dev\\Python312\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\dev\\Python312\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\dev\\Python312\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\dev\\Python312\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\dev\\Python312\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\dev\\Python312\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\dev\\Python312\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\dev\\Python312\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\dev\\Python312\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\dev\\Python312\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\dev\\Python312\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\dev\\Python312\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\dev\\Python312\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\dev\\Python312\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\dev\\Python312\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\dev\\Python312\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\dev\\Python312\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\dev\\Python312\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\dev\\Python312\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\dev\\Python312\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\dev\\Python312\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\dev\\Python312\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\dev\\Python312\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\dev\\Python312\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\dev\\Python312\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\dev\\Python312\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\dev\\Python312\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\dev\\Python312\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\dev\\Python312\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\dev\\Python312\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'D:\\dev\\Python312\\Lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'D:\\dev\\Python312\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\dev\\Python312\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\dev\\Python312\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'D:\\dev\\Python312\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312',
   'D:\\dev\\Python312\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\dev\\Python312\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\dev\\Python312\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\dev\\Python312\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\dev\\Python312\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\dev\\Python312\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\dev\\Python312\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\dev\\Python312\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\dev\\Python312\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\dev\\Python312\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\dev\\Python312\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\dev\\Python312\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\dev\\Python312\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\dev\\Python312\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\dev\\Python312\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\dev\\Python312\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\dev\\Python312\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\dev\\Python312\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\dev\\Python312\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\dev\\Python312\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\dev\\Python312\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\dev\\Python312\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\dev\\Python312\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\dev\\Python312\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\dev\\Python312\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\dev\\Python312\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\dev\\Python312\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\dev\\Python312\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\dev\\Python312\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\dev\\Python312\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\dev\\Python312\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\dev\\Python312\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\dev\\Python312\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\dev\\Python312\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\dev\\Python312\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\dev\\Python312\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\dev\\Python312\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\dev\\Python312\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\dev\\Python312\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\dev\\Python312\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\dev\\Python312\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\dev\\Python312\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\dev\\Python312\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\dev\\Python312\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\dev\\Python312\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\dev\\Python312\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\dev\\Python312\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\dev\\Python312\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\dev\\Python312\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'D:\\dev\\Python312\\Lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\dev\\Python312\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\dev\\Python312\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\dev\\Python312\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings', 'D:\\dev\\Python312\\Lib\\encodings\\__init__.py', 'PYMODULE'),
  ('reprlib', 'D:\\dev\\Python312\\Lib\\reprlib.py', 'PYMODULE'),
  ('re._parser', 'D:\\dev\\Python312\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'D:\\dev\\Python312\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'D:\\dev\\Python312\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'D:\\dev\\Python312\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('re', 'D:\\dev\\Python312\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('enum', 'D:\\dev\\Python312\\Lib\\enum.py', 'PYMODULE'),
  ('ntpath', 'D:\\dev\\Python312\\Lib\\ntpath.py', 'PYMODULE'),
  ('codecs', 'D:\\dev\\Python312\\Lib\\codecs.py', 'PYMODULE'),
  ('collections.abc',
   'D:\\dev\\Python312\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\dev\\Python312\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('stat', 'D:\\dev\\Python312\\Lib\\stat.py', 'PYMODULE'),
  ('heapq', 'D:\\dev\\Python312\\Lib\\heapq.py', 'PYMODULE'),
  ('locale', 'D:\\dev\\Python312\\Lib\\locale.py', 'PYMODULE'),
  ('operator', 'D:\\dev\\Python312\\Lib\\operator.py', 'PYMODULE'),
  ('keyword', 'D:\\dev\\Python312\\Lib\\keyword.py', 'PYMODULE'),
  ('types', 'D:\\dev\\Python312\\Lib\\types.py', 'PYMODULE'),
  ('abc', 'D:\\dev\\Python312\\Lib\\abc.py', 'PYMODULE'),
  ('genericpath', 'D:\\dev\\Python312\\Lib\\genericpath.py', 'PYMODULE'),
  ('_weakrefset', 'D:\\dev\\Python312\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('warnings', 'D:\\dev\\Python312\\Lib\\warnings.py', 'PYMODULE'),
  ('sre_parse', 'D:\\dev\\Python312\\Lib\\sre_parse.py', 'PYMODULE'),
  ('sre_compile', 'D:\\dev\\Python312\\Lib\\sre_compile.py', 'PYMODULE'),
  ('os', 'D:\\dev\\Python312\\Lib\\os.py', 'PYMODULE')])
